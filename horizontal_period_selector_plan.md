# Horizontal Period Selector Implementation Plan

## Overview
Create an infinite horizontal scroll period selector positioned under the app bar with format "Jul 25 | Jun 25 | Aug 25 | Sep 25". The existing calendar icon functionality remains completely unchanged.

## Phase 1: Core Infrastructure

### 1.1 Extend TimePeriodService
- [ ] Add `generatePeriodsAround(TimePeriod center, int bufferSize)` method
- [ ] Add `getRelativePeriod(TimePeriod base, int offset)` method  
- [ ] Add `formatPeriodCompact(TimePeriod period)` returning "Jul 25" format
- [ ] Add period caching with LRU eviction for performance
- [ ] Add infinite period generation logic for past and future

### 1.2 Create HorizontalPeriodSelector Widget
- [ ] Create new file: `lib/features/common/widgets/horizontal_period_selector.dart`
- [ ] Implement ConsumerWidget with ListView.builder (horizontal scrollDirection)
- [ ] Add lazy loading with 20-period buffer (10 each side)
- [ ] Implement auto-scroll to center selected period on initialization
- [ ] Add scroll boundary detection for infinite loading
- [ ] Apply Material 3 design with proper spacing and separators
- [ ] Synchronize with existing timePeriodNotifierProvider state
- [ ] Add proper accessibility semantics

### 1.3 Create PeriodScrollController
- [ ] Create new file: `lib/features/common/widgets/period_scroll_controller.dart`
- [ ] Implement custom ScrollController for infinite scroll state management
- [ ] Add scroll position persistence
- [ ] Implement debounced scroll listeners for performance
- [ ] Add smooth scroll animations for period selection
- [ ] Handle scroll edge cases and boundary conditions

## Phase 2: Integration

### 2.1 Update AppBar Helper Methods
- [ ] Modify `lib/features/common/widgets/app_bar_helpers.dart`
- [ ] Add new method: `createAppBarWithHorizontalPeriodSelector()`
- [ ] Return Column widget containing AppBar + HorizontalPeriodSelector
- [ ] Keep all existing AppBar methods unchanged
- [ ] Maintain existing calendar icon functionality completely

### 2.2 Update Screen Implementations
- [ ] Update `lib/features/budgets/presentation/screens/budgets_list_screen.dart`
  - [ ] Keep existing calendar icon AppBar unchanged
  - [ ] Add HorizontalPeriodSelector widget below AppBar
  - [ ] Ensure proper layout with Expanded widgets
- [ ] Update `lib/features/transactions/presentation/screens/transactions_list_screen.dart`
  - [ ] Keep existing calendar icon AppBar unchanged
  - [ ] Add HorizontalPeriodSelector widget below AppBar
  - [ ] Ensure proper layout with Expanded widgets

### 2.3 Dual Selector Synchronization
- [ ] Ensure both calendar modal and horizontal selector use same timePeriodNotifierProvider
- [ ] Implement bidirectional state updates
- [ ] Test changes from calendar modal reflect in horizontal selector
- [ ] Test changes from horizontal selector trigger existing onPeriodChanged callbacks
- [ ] Verify no conflicts between selection methods

## Phase 3: Polish & Testing

### 3.1 Performance Optimization
- [ ] Implement viewport-aware rendering
- [ ] Add scroll position caching mechanism
- [ ] Optimize widget rebuild strategies
- [ ] Add memory management for infinite periods
- [ ] Profile scroll performance for 60fps target
- [ ] Add loading indicators for smooth UX

### 3.2 Testing Implementation
- [ ] Create `test/features/common/widgets/horizontal_period_selector_test.dart`
- [ ] Add widget tests for scroll behavior
- [ ] Add dual selector synchronization tests
- [ ] Add integration tests with existing calendar modal
- [ ] Add performance tests for infinite scroll
- [ ] Add accessibility compliance tests
- [ ] Test edge cases (network issues, memory limits)

### 3.3 UI/UX Polish
- [ ] Implement smooth scroll animations
- [ ] Add loading states and error handling
- [ ] Ensure accessibility semantics are complete
- [ ] Verify consistent Material 3 design
- [ ] Test proper spacing below AppBar
- [ ] Ensure responsive design for different screen sizes
- [ ] Add haptic feedback for period selection

## Technical Specifications

### Format Requirements
- **Current modal format**: "December 2024" (unchanged)
- **New horizontal format**: "Jul 25 | Jun 25 | Aug 25 | Sep 25"
- **Separator**: " | " (space pipe space)
- **Month**: 3-letter abbreviation (Jan, Feb, Mar, etc.)
- **Year**: 2-digit format (25, 26, etc.)

### Layout Structure
```dart
Column(
  children: [
    AppBar(...), // Existing with calendar icon - NO CHANGES
    HorizontalPeriodSelector(), // New infinite scroll widget
    Expanded(
      child: ScreenContent(), // Existing screen body
    ),
  ],
)
```

### State Management
- [ ] Use existing `timePeriodNotifierProvider`
- [ ] Both selectors watch same provider
- [ ] Period changes update global state
- [ ] Horizontal selector auto-scrolls to new selection
- [ ] Maintain all existing callback functionality

### Performance Targets
- [ ] Maintain 60fps during scroll
- [ ] Memory usage under 50MB for infinite periods
- [ ] Initial load time under 100ms
- [ ] Smooth animations at 60fps
- [ ] Responsive touch interactions

## Success Criteria

### Functional Requirements
- [ ] Calendar icon functionality completely unchanged
- [ ] Horizontal selector works independently
- [ ] Both selectors synchronized perfectly
- [ ] Format matches exactly: "Jul 25 | Jun 25 | Aug 25 | Sep 25"
- [ ] Smooth infinite horizontal scrolling
- [ ] Auto-centers on current/selected period
- [ ] Lazy loading with proper buffer management

### Quality Requirements
- [ ] Zero breaking changes to existing screens
- [ ] Comprehensive test coverage (>90%)
- [ ] Accessible design (screen readers, semantic labels)
- [ ] Responsive design (phones, tablets)
- [ ] Performance meets 60fps target
- [ ] Memory efficient infinite scrolling

### Integration Requirements
- [ ] Works with existing Riverpod state management
- [ ] Maintains existing onPeriodChanged callbacks
- [ ] Compatible with existing Material 3 theme
- [ ] Follows established architecture patterns
- [ ] No conflicts with existing navigation

## Package Recommendations

### Primary Approach: Custom Implementation
- **Reason**: Full control over integration with existing architecture
- **Base**: ListView.builder with horizontal scrollDirection
- **Benefits**: Perfect integration, optimal performance, exact format control

### Alternative Packages (if needed)
- [ ] **infinite_scroll_pagination**: For complex lazy loading scenarios
- [ ] **easy_date_timeline**: If custom implementation proves insufficient
- [ ] **Built-in PageView**: For alternative scroll behavior

## Risk Mitigation
- [ ] Existing functionality preserved 100%
- [ ] Horizontal selector is pure addition
- [ ] Feature flag for gradual rollout
- [ ] Calendar modal always available as fallback
- [ ] Performance monitoring during development
- [ ] Comprehensive testing before deployment
- [ ] Rollback plan if issues arise

## Dependencies
- [ ] No new package dependencies required
- [ ] Uses existing Riverpod providers
- [ ] Leverages existing TimePeriod models
- [ ] Works with current Material 3 theme
- [ ] Compatible with existing test infrastructure

## Delivery Timeline
- **Phase 1**: 2 days (Core infrastructure)
- **Phase 2**: 1 day (Integration)
- **Phase 3**: 2 days (Polish & Testing)
- **Total**: 5 days estimated

---
*This plan creates a horizontal infinite scroll period selector that complements (not replaces) the existing calendar icon functionality.*