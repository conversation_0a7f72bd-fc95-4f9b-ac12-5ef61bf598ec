# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **MAJOR: UI/UX Enhancement Complete**: Comprehensive interface modernization with consistent app bar system and enhanced navigation
  - **Modern App Bar System**: Textual add buttons with "+" prefix across all screens (+ Add Account, + Add Goal, + Add Tag, + Add Category, + Edit Budget)
  - **Enhanced Navigation**: Left-aligned titles for better visual hierarchy, calendar period selector button in app bar actions
  - **Streamlined Interface**: Removed app bar back buttons across all screens, eliminated unnecessary X buttons from filtered transaction views
  - **Profile Reorganization**: Restructured with logical sections (Account Management, Data Management, App Settings, Tools & Reports)
  - **Categories/Tags Navigation**: Moved under settings hierarchy with proper back and home FAB navigation
  - **FAB System Enhancement**: Unique hero tags using instance IDs to prevent conflicts, improved route-based visibility logic
  - **Form Screen Integration**: Enhanced form FAB system with proper form vs navigation screen detection
  - **Budgets Simplification**: Removed complex select/copy functionality, streamlined to edit-only operations (122 lines removed)
  - **Quality Assurance**: 6/7 FAB navigation tests passing (85.7% success rate), Hero tag conflicts resolved, clean analyzer

- **PREVIOUS: Hub-Based Navigation System**: Complete navigation system refactoring from bottom navigation to modern hub-based navigation
  - **Hub Home Screen**: Central navigation hub with 6 feature cards in responsive 2x3 grid layout (Accounts, Transactions, Budgets, Goals, Statistics, Settings)
  - **Global FAB System**: 3-FAB system with Material 3 design:
    - Green FAB (bottom right): Add transaction - available on all screens for consistent action access
    - Grey FAB (bottom left): Back navigation - appears on all screens except home
    - Grey FAB (center bottom): Home navigation - for screens deeper than one step from home
  - **Route Architecture**: Simplified go_router configuration with top-level GoRoutes replacing complex ShellRoute system
  - **FAB Visibility Management**: Route-based visibility logic with FabVisibilityNotifier for intelligent FAB display
  - **Extension Method Integration**: GlobalFabSystemExtension provides `.withGlobalFabs()` for easy screen integration
  - **Accounts Enhancement**: Net Worth summary card with real-time assets/liabilities calculation and Material 3 styling
  - **Screen Integration**: All major screens (Transactions, Budgets, Goals, Accounts) updated to use global FAB system
  - **Material 3 Compliance**: Fixed deprecated patterns including `withOpacity` calls, updated to `withValues(alpha:)`
  - **Quality Assurance**: Clean analyzer, proper formatting, comprehensive testing completed

### Fixed
- **Hero Tag Conflicts**: Resolved "multiple heroes with same tag" exceptions by implementing unique hero tags using instance IDs
- **FAB Visibility Logic**: Enhanced route-based FAB visibility to properly distinguish between form screens and navigation screens
- **App Bar Consistency**: Standardized app bar behavior across all screens with consistent title positioning and action buttons
- **Navigation Flow**: Improved categories and tags screen navigation with proper back and home FAB integration
- **Code Cleanup**: Removed 122 lines of unused selection/copy functionality from budgets screen for cleaner codebase
- **Test Coverage**: Improved FAB navigation test success rate from previous failures to 6/7 tests passing (85.7%)

- **PREVIOUS: Material 3 Compatibility**: Fixed all deprecated Material 3 patterns for future Flutter compatibility
  - **Color API**: Updated `withOpacity()` calls to use `withValues(alpha:)` for Material 3 compliance
  - **FontWeight**: Fixed `FontWeight.medium` usage to use `FontWeight.w500` for consistency
  - **Analyzer Issues**: Resolved all analyzer warnings and unused import issues
- **PREVIOUS: Navigation Architecture**: Simplified complex routing system for better maintainability
  - **ShellRoute Removal**: Eliminated complex nested shell route architecture
  - **Route Simplification**: Converted all routes to clean top-level GoRoute configuration
  - **Import Cleanup**: Removed unused navigation imports and dependencies

### Changed
- **Major Dependency Updates**: Updated all Firebase and core dependencies to latest stable versions
  - **Firebase Suite**: Updated all Firebase packages (Auth 5.7.0, Firestore 5.6.12, Analytics 11.6.0, etc.)
  - **Freezed**: Upgraded from 2.5.7 to 3.1.0 with full sealed class migration
  - **Go Router**: Updated from 12.1.0 to 16.0.0 with compatibility fixes
  - **Google Sign-In**: Updated from 6.2.1 to 7.1.1 (implementation pending)
  - **Build Tools**: Updated build_runner, flutter_lints, very_good_analysis to latest versions
  - **Impact**: Enhanced security, performance, and access to latest Flutter/Firebase features

### Security
- **CRITICAL: Fixed Command Injection Vulnerability**: Resolved critical security vulnerability in development environment setup script
  - **Issue**: `curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -` pattern in `.augment/env/setup.sh` allowed potential command injection
  - **Fix**: Implemented secure script verification by downloading to temporary file, verifying NodeSource origin, then executing with proper cleanup
  - **Impact**: Eliminated critical security risk while maintaining Node.js installation functionality
  - **Verification**: All 5,038 tests passing, flutter analyze clean, comprehensive security review completed

### Added
- **Unified Profile Management Screen Implementation**: Complete profile management feature with tabbed interface and comprehensive functionality
  - **Three-Tab Interface**: Implemented Profile, Password, and Security tabs with proper TabController and Material 3 design
  - **Profile Tab**: Display name and email editing with smart cross-field validation allowing either field to be empty
  - **Password Tab**: Secure password change functionality with re-authentication and comprehensive validation
  - **Security Tab**: Biometric authentication settings with proper error handling and setup guidance
  - **Advanced Form Validation**: Cross-field validation logic that allows flexible profile updates with user-friendly error messages
  - **Loading States**: Proper loading indicators and disabled states during async operations with visual feedback
  - **Error Handling**: Comprehensive error handling with SnackBar feedback and specific error messages for different scenarios
  - **Test Coverage**: 75 comprehensive tests covering all functionality, edge cases, error scenarios, and user interactions
  - **Mock Infrastructure Enhancement**: Enhanced MockProviders with biometric support, UserProfile fallback values, and TestBiometricAuthNotifier
  - **UI Components**: UnifiedProfileManagementScreen with TabController, BiometricSettingsTile, BiometricErrorBanner integration
  - **Navigation Integration**: Seamless GoRouter integration with proper route handling and back navigation
  - **Security Features**: Re-authentication for password changes and biometric authentication management
  - **Material 3 Compliance**: Complete Material 3 design compliance with proper theming, typography, and accessibility
  - **Quality Assurance**: All tests passing with clean static analysis (0 issues) and proper code formatting
  - **Technical Achievement**: Production-ready unified profile management system providing comprehensive user account management with security features and biometric integration
- **TagsListScreen Test Coverage Enhancement**: Added provider-level tests for TagsListScreen
  - **Test File Created**: Created `test/features/tags/presentation/screens/tags_list_screen_test.dart` with 3 test cases
  - **Test Cases**: Implemented tests for userTagsProvider, tagSearchProvider search functionality, and tagSearchProvider clear search functionality
  - **Testing Approach**: Used ProviderContainer with mocked TagRepository to test provider behavior in isolation
  - **Technical Achievement**: Established foundation for testing TagsListScreen with proper provider testing patterns
- **AccountsListScreen Test Coverage Enhancement**: Improved test coverage from 1.2% to 87.1% with comprehensive test suite
  - **Test File Created**: Created `test/features/accounts/presentation/screens/accounts_list_screen_test.dart` with 7 test cases
  - **Test Cases**: Implemented tests for empty state, account list display, section headers, error state, add button, primary/inactive badges, and pull-to-refresh
  - **Testing Approach**: Used TestWrapper.createTestWidget with mocked accountListProvider to test different states (loading, data, error)
  - **Documentation**: Created TEST_COVERAGE_IMPROVEMENTS.md documenting the approach, results, and future improvements
  - **Code Quality**: All tests passing with clean static analysis and proper code formatting
  - **Technical Achievement**: Significant test coverage improvement for a core screen component ensuring reliable behavior across various scenarios
- **Firebase Security Rules Testing Enhancement**: Comprehensive business logic validation and edge case testing for Firebase Security Rules
  - **Business Logic Validation Tests**: Created comprehensive test suite for hierarchical category relationships, account-transaction consistency, budget-category relationships, and tag-transaction relationships
  - **Edge Case and Boundary Tests**: Implemented thorough testing for string length boundaries (account names 100 chars, tag names 50 chars, transaction notes 500 chars), numeric boundaries (transaction amounts up to 999,999,999 cents), date edge cases (10 years ago valid, future dates restricted), and Unicode/special character support
  - **Enhanced Test Coverage**: Added 28 new test cases covering business logic validation and boundary conditions across all entity types
  - **Security Validation**: Comprehensive testing for malicious input scenarios, field tampering prevention, and cross-entity relationship validation
  - **Documentation**: Created detailed Firebase Security Rules Testing documentation with test organization, validation rules, and maintenance guidelines
  - **Quality Assurance**: All new tests passing with proper Firebase emulator integration and comprehensive validation coverage
- **Contribution Entry UI Implementation (Task 15.6)**: Complete frontend UI implementation for goal contribution tracking with comprehensive form system and Material 3 design
  - **Form Configuration System**: Created GoalContributionFormConfig with create/edit configurations and proper validation for amounts (positive, ≤$1M), dates (not future, ≤10 years ago), and descriptions (≤500 chars)
  - **Comprehensive Riverpod Providers**: Implemented goalContributionsProvider, activeGoalContributionsProvider, goalContributionCreationProvider, goalContributionUpdateProvider, goalContributionDeletionProvider with real-time streams and automatic provider invalidation
  - **UI Screens**: Complete screen implementation including GoalContributionCreateScreen, GoalContributionEditScreen, GoalContributionsListScreen with proper navigation, error handling, and Material 3 design
  - **ContributionCard Widget**: Material 3 styled card with smart date formatting (Today, Yesterday, This Week), currency display using global currencyFormatterProvider, edit/delete menu actions with confirmation dialogs
  - **App Router Integration**: Added contribution routes (/goals/:id/contributions, /goals/:id/contributions/create, /goals/:id/contributions/:contributionId/edit) with proper parameter passing and navigation
  - **Enhanced Goal Cards**: Added "Add Contribution" and "View Contributions" menu actions to goal cards for seamless user experience and easy access to contribution management
  - **Real-Time Updates**: Automatic data updates and provider invalidation ensuring real-time contribution tracking across all screens with immediate UI feedback
  - **Form Validation**: Comprehensive business rules validation with user-friendly error messages, real-time feedback, and proper error handling throughout the contribution workflow
  - **User Experience**: Empty state handling with encouraging messaging, pull-to-refresh functionality, smart date formatting, loading states, and confirmation dialogs for destructive actions
  - **Quality Assurance**: All 779 tests passing with clean static analysis (fixed strict_raw_type warning), proper code formatting, and production-ready implementation
  - **Technical Achievement**: Production-ready goal contribution tracking with comprehensive form system, real-time updates, Material 3 design compliance, and seamless integration with existing goals feature architecture
- **Goal Contribution Repository Implementation (Task 15.4)**: Complete Firestore Repository for Goal Contribution Management with comprehensive subcollection management and analytics capabilities
  - **Complete Repository Interface**: Created IGoalContributionRepository interface with 24 methods covering all contribution management operations including CRUD, analytics, filtering, and validation
  - **Repository Implementation**: Implemented GoalContributionRepositoryImpl following established BudApp patterns with FirestoreService and FirebaseAuth integration, proper error handling, and subcollection path management
  - **CRUD Operations**: Full create, read, update, delete operations with proper user isolation, document ID generation, timestamp management, and soft deletion using isActive flag
  - **Real-Time Streams**: Implemented watchContributionsForGoal, watchActiveContributionsForGoal, watchAllUserContributions, and watchContribution for live data updates using Firestore snapshots
  - **Goal-Specific Operations**: Comprehensive goal contribution management including getContributionsForGoal, getTotalContributionAmountForGoal, getContributionCountForGoal, and getRecentContributions
  - **User-Level Operations**: Cross-goal contribution queries with getAllUserContributions, getActiveUserContributions for dashboard and analytics views
  - **Date-Based Filtering**: Advanced filtering capabilities with getContributionsByDateRange, getTodaysContributions, getThisWeeksContributions, getThisMonthsContributions
  - **Analytics & Statistics**: Comprehensive statistics calculation with getContributionStatistics including total amounts, counts, averages, and date ranges
  - **Validation System**: Integration with GoalContribution model's built-in validation system, goal existence validation, and comprehensive business rule enforcement
  - **Provider Integration**: Successfully integrated with existing repository provider system, added goalContributionRepositoryProvider to repository_providers.dart, exported IGoalContributionRepository in repositories.dart
  - **Comprehensive Testing**: Created test suite with 22 passing tests covering all CRUD operations, goal-specific operations, user-level operations, real-time streams, validation, and error handling (2 tests skipped due to fake_cloud_firestore date range query limitations)
  - **Documentation**: Created comprehensive Goal Contribution Repository documentation (docs/GOAL_CONTRIBUTION_REPOSITORY.md) with implementation details, usage patterns, testing strategies, and integration guidelines
  - **Code Quality**: Clean Flutter analyze (0 issues), proper code formatting, following established architectural patterns and naming conventions
  - **Technical Achievement**: Production-ready foundation for goal contribution management feature with comprehensive data access layer, real-time updates, and seamless integration with BudApp's established architectural patterns
- **Goal Contribution Data Model Implementation (Task 15.2)**: Comprehensive Firestore GoalContribution Data Model for tracking individual contributions towards financial goals
  - **Complete Freezed Model**: Implemented GoalContribution model with JSON serialization following BudApp patterns
  - **Required Fields**: All specified fields including id, userId, goalId, amountCents, contributionDate, description plus standard BudApp fields
  - **Subcollection Path**: users/{userId}/goals/{goalId}/contributions following established BudApp security and performance patterns
  - **Validation System**: Comprehensive validation with 11 business rules covering amount validation (positive, ≤$100M), date validation (not future, ≤10 years ago), description validation (≤500 chars)
  - **Date Helper Methods**: Built-in methods for isMadeToday, isMadeThisWeek, isMadeThisMonth, dateDescription with smart date formatting
  - **Utility Methods**: hasDescription, summaryMessage for UI display and user-friendly contribution summaries
  - **Factory Methods**: GoalContribution.create(), GoalContribution.fromJson(), GoalContribution.fromFirestore() with proper defaults and backward compatibility
  - **Documentation**: Complete schema documentation (docs/GOAL_CONTRIBUTION_DATA_SCHEMA.md) with security rules, indexing strategy, query patterns, and usage examples
  - **Testing**: Comprehensive test suite with 27 test cases covering creation, serialization, validation, date helpers, edge cases achieving 100% test coverage
  - **Technical Achievement**: Production-ready foundation for goal contribution tracking with proper validation, security, and performance considerations ready for repository implementation
- **Goal Data Model Implementation (Task 15.1)**: Comprehensive Firestore Goal Data Model for financial goal tracking feature
  - **Complete Freezed Model**: Implemented Goal model with JSON serialization following BudApp patterns
  - **Required Fields**: All specified fields including id, name, targetAmountCents, currentAmountCents, targetDate, isCompleted, colorHex, iconName, schemaVersion
  - **GoalStatus Enum**: Proper JSON values for active, paused, completed, cancelled states
  - **Collection Path**: users/{userId}/goals following established BudApp security patterns
  - **Validation System**: Comprehensive validation with business rules, format validation, and consistency checks
  - **Progress Calculations**: Built-in methods for progressPercentage, remainingAmountCents, isAchieved, daysRemaining, requiredDailySavingsCents
  - **Factory Methods**: Goal.create(), Goal.fromJson(), Goal.fromFirestore() with backward compatibility
  - **Backward Compatibility**: Handles migration from boolean isCompleted to GoalStatus enum
  - **Documentation**: Complete schema documentation with security rules, indexing strategy, and usage examples
  - **Testing**: Comprehensive test suite with 24 test cases covering creation, serialization, validation, calculations, and utilities
  - **Technical Achievement**: Production-ready foundation for financial goal tracking with proper validation, security, and performance considerations
- **Comprehensive Static Analysis Configuration (Task 31.6)**: Established comprehensive code quality standards and static analysis infrastructure
  - **Analysis Configuration**: Configured analysis_options.yaml with flutter_lints foundation plus 50+ additional quality rules for performance, security, and maintainability
  - **Issue Reduction**: Reduced linting issues from 4,393 (with very_good_analysis) to 1,837 manageable issues through gradual adoption strategy
  - **Documentation Creation**: Created comprehensive Code Quality Standards Guide (docs/code_quality_standards.md) with detailed implementation roadmap
  - **Developer Onboarding**: Developed Quick Setup Guide (docs/quick_setup_guide.md) with 5-minute setup process and daily workflow integration
  - **README Integration**: Updated main README.md with dedicated code quality section and current status
  - **Gradual Adoption Strategy**: Established phase-based approach for quality improvement (Week 1: critical errors, Week 2: automated fixes, Week 3: documentation, Week 4: stricter rules)
  - **CI/CD Foundation**: Provided foundation for quality gates, pre-commit hooks, and automated quality enforcement
  - **Issue Categorization**: Organized 1,837 issues into manageable categories (1,500+ documentation warnings, 200+ import sorting, 100+ constructor ordering, 30+ performance, 5 critical errors)
  - **Automated Fixes**: Many issues can be resolved with `dart fix --apply` and `dart format .` for quick quality improvements
  - **Technical Achievement**: Production-ready code quality infrastructure with clear improvement pathway while maintaining development velocity
- **Enhanced Firestore Security Rules (Task 33.7)**: Comprehensive security rules implementation with 2024 best practices
  - **Enhanced Security Functions**: Added input sanitization, financial amount validation, rate limiting framework, and business hours validation
  - **Comprehensive Validation**: Implemented detailed validation functions for all entity types (accounts, transactions, categories, budgets, goals, tags)
  - **User Data Isolation**: Strict ownership validation ensuring users can only access their own data
  - **Authentication Required**: All operations require authenticated users with valid Firebase Auth tokens
  - **Data Validation**: Enforce field types, required fields, and business rules at the database level
  - **Referential Integrity**: Basic deletion constraints with app-level enforcement requirements documented
  - **Premium Feature Framework**: Account limits and subscription-based feature enforcement structure
  - **Security Testing**: Comprehensive test suite validating all security rules and validation functions
  - **Documentation Updated**: Enhanced Firebase Firestore documentation with security implementation details
  - **Test Compatibility**: Fixed security rules tests to match actual implementation patterns
- **Flutter Testing Suite Refactoring Research Complete (Task 33.1)**: Comprehensive research and planning phase for testing suite refactoring project
  - **Research Findings**: Documented fake_cloud_firestore security rules testing capabilities and firebase_auth_mocks integration patterns
  - **Current State Analysis**: Analyzed 60+ test files identifying fragmented mocking patterns and maintenance overhead
  - **Migration Strategy**: Created detailed 5-phase implementation plan targeting 40-60% reduction in test setup complexity
  - **Dependencies Updated**: Added firebase_auth_mocks: ^0.14.2 to dev_dependencies for enhanced auth testing
  - **Documentation Created**: Comprehensive research documentation, current patterns analysis, and implementation plan
  - **Foundation Established**: Clear roadmap for transforming to robust standardized testing approach with security rules testing
  - **Benefits Identified**: Enhanced test reliability, reduced maintenance overhead, standardized patterns across all test files
  - **Next Phase Ready**: Project prepared for Task 33.2 (Establish New Testing Foundation) with detailed technical specifications
- **Firebase Testing Foundation Established (Task 33.2)**: Successfully created comprehensive Firebase testing foundation with standardized utilities
  - **Firebase Test Setup**: Created FirebaseTestSetup class for unified Firebase service testing with firebase_auth_mocks + fake_cloud_firestore integration
  - **Authentication Testing**: Developed TestAuthHelper utilities with MockUser creation patterns, auth scenarios, and user type factories
  - **Security Rules Testing**: Implemented SecurityRulesHelper framework enabling actual security rules testing with auth state integration
  - **Enhanced Mock Data**: Extended MockDataFactory with Firebase integration methods and comprehensive test data factories
  - **Standardized Patterns**: Established unified testing approach replacing fragmented mocking patterns with robust standardized foundation
  - **Real Firebase Behavior**: Tests now closer to production Firebase behavior with security rules testing capabilities
  - **Code Reduction**: Foundation enables 40-60% reduction in test setup complexity across 60+ test files
  - **Migration Ready**: Authentication tests migration prepared with clear patterns and comprehensive utilities
- **Authentication Tests Migration Complete (Task 33.3)**: Successfully migrated authentication tests to firebase_auth_mocks with enhanced reliability
  - **Auth Service Migration**: Replaced custom MockFirebaseAuth with firebase_auth_mocks in auth service tests
  - **Mock Providers Update**: Updated core mock_providers.dart to use firebase_auth_mocks for central mocking system
  - **Provider Overrides Enhanced**: Improved authenticatedUserOverrides and unauthenticatedUserOverrides with firebase_auth_mocks integration
  - **Reset Compatibility**: Fixed mocktail reset() incompatibility with firebase_auth_mocks instances
  - **Test Reliability**: All 44 authentication tests passing with real Firebase Auth behavior simulation
  - **Simplified Setup**: Eliminated 50+ lines of custom mock implementation with standardized TestAuthHelper patterns
  - **Backward Compatibility**: Maintained existing test APIs ensuring no breaking changes for consumers
  - **Migration Template**: Established clear patterns for repository tests migration in next phase
- **Repository Tests Migration Complete (Task 33.4)**: Successfully migrated all repository tests to firebase_auth_mocks + fake_cloud_firestore integration
  - **Comprehensive Migration**: Migrated 110 repository tests across transaction, account, user, and integration test files
  - **Authentication State Fix**: Fixed critical authentication state issue in FirebaseTestSetup requiring signedIn parameter
  - **Enhanced Provider Integration**: Updated all repository provider tests to use FirebaseTestSetup patterns
  - **Security Rules Foundation**: Established foundation for comprehensive security rules testing with auth integration
  - **Test Reliability**: All 110 repository tests passing with real Firebase Auth + Firestore behavior simulation
  - **Code Simplification**: Eliminated 100+ lines of custom mock setup code with standardized patterns
  - **Pattern Standardization**: Unified testing approach across all repository implementations
  - **Integration Testing**: Enhanced budget-transaction integration tests with real auth context
- **Service and Widget Tests Migration Complete (Task 33.5)**: Successfully completed migration of service and widget tests to new Firebase testing foundation
  - **Selective Migration Approach**: Identified and migrated only tests requiring Firebase integration (2 integration test files)
  - **Service Tests Verification**: Validated 55 service tests using appropriate mocktail patterns for dependency mocking
  - **Widget Tests Validation**: Confirmed 7 widget tests working with updated MockProviders using firebase_auth_mocks integration
  - **Integration Tests Enhancement**: Migrated transaction balance and account repository integration tests to FirebaseTestSetup
  - **Testing Architecture Integrity**: Maintained correct patterns with service layer using dependency mocking and integration layer using Firebase simulation
  - **Comprehensive Validation**: Achieved 100% test compatibility across 227+ tests with enhanced reliability
  - **Code Quality**: All code passes flutter analyze with 0 issues
  - **Foundation Readiness**: Established comprehensive testing foundation ready for security rules validation
- **Material 3 Floating Label Forms Complete**: Comprehensive Material 3 floating label implementation across all form inputs
  - **Enhanced Text Field Components**: AppTextFormField and AuthFormField now feature Material 3 floating labels with `FloatingLabelBehavior.auto`
  - **Transaction Forms Enhancement**: AmountInputField, DescriptionField, and NotesField converted to use Material 3 floating labels while preserving specialized functionality
  - **Budget Forms Enhancement**: BudgetForm, CategoryBudgetCard, and BulkBudgetOperationsDialog converted to use Material 3 floating labels
  - **100% Coverage**: All form inputs across authentication, profile, entity, transaction, and budget screens now use consistent Material 3 floating label behavior
  - **Backward Compatibility**: Legacy static label mode available via `useFloatingLabel: false` parameter for specialized use cases
  - **Quality Assurance**: All 554 tests passing, flutter analyze clean, consistent code formatting maintained
- **Shared Form Components**: Unified shared color and icon picker components across all entity forms for consistency and code reusability
  - **Critical Fix**: Resolved type casting error in AccountFormConfig where generic FormFieldConfig<String> was incorrectly used for color and icon fields
  - **Specialized Configurations**: Replaced with proper ColorPickerFieldConfig and IconPickerFieldConfig classes with entity-specific constants
  - **Consistency Implementation**: Updated AccountFormConfig to match CategoryFormConfig pattern using FormConstants.accountColors and FormConstants.accountIcons
  - **Form Field Factory**: Enhanced factory with proper type handling for specialized field configurations
  - **Entity Integration**: All entity forms (Account, Category, Tag) now use consistent shared picker components
  - **Type Safety**: Eliminated runtime type casting errors through proper specialized configuration classes
  - **Testing Coverage**: All 554 tests passing including previously failing account edit/create screen tests
  - **Code Quality**: Flutter analyze clean, dart format applied, comprehensive documentation created
  - **User Experience**: Consistent color and icon selection interface across all entity creation and editing screens
  - **Architecture Benefits**: Reduced code duplication, improved maintainability, established reusable component patterns
- **Repository Firebase Auth Integration**: Resolved critical Firebase Auth integration issues across all repository implementations
  - **Account Repository Fix**: Added Firebase Auth dependency to AccountRepositoryImpl with automatic user ID assignment in createAccount method
  - **Category Repository Fix**: Updated CategoryRepositoryImpl create methods to automatically set current user ID, preventing collection path errors
  - **Collection Path Resolution**: Fixed "//" collection path errors in both account and category creation by ensuring proper user context
  - **Test Infrastructure**: Updated all repository test files with proper Firebase Auth mocking using MockFirebaseAuth and MockUser classes
  - **Database Performance**: Added missing Firestore composite index for category queries and deployed to Firebase
  - **Provider Updates**: Updated accountRepositoryProvider to inject Firebase Auth dependency alongside FirestoreService
  - **Error Handling**: Implemented meaningful error messages when Firebase Auth is not provided in repository operations
  - **Testing Coverage**: All 554 tests passing with comprehensive Firebase Auth mocking patterns established
  - **User Experience**: Reliable account and category creation without runtime collection path errors
  - **Architecture Benefits**: Consistent Firebase Auth integration pattern, proper user data isolation, automatic user context assignment
- **Generic Form System Complete**: Comprehensive configuration-driven form architecture eliminating code duplication across all entity forms
  - **Core Architecture**: Implemented GenericFormScreen<T> and GenericFormConfig<T> system handling all entity types through unified patterns
  - **Form Field System**: Created FormFieldFactory with specialized field types (ColorPickerFieldConfig, IconPickerFieldConfig, AccountTypeFormFieldConfig)
  - **Shared Components**: Established unified ColorPickerFormFieldImpl and IconPickerFormFieldImpl across all entities with entity-specific constants
  - **Entity Integration**: Updated Account, Category, Transaction, Budget, and Tag forms to use generic system with consistent patterns
  - **Code Reduction**: Achieved 60-80% reduction in form-related code while maintaining full functionality and type safety
  - **Type Safety**: Implemented compile-time type checking for all form configurations and automatic data mapping
  - **Navigation Consistency**: Established standardized full-screen navigation patterns with proper AppBar structure across all forms
  - **Validation System**: Unified field-level and form-level validation with consistent error handling and user feedback
  - **Repository Integration**: Seamless integration with existing repository pattern and Firebase Auth automatic user ID assignment
  - **Testing Coverage**: Comprehensive testing with all 554 tests passing including form workflow and validation tests
  - **Documentation**: Complete documentation with implementation guides, code examples, and architecture benefits including new Generic Form Architecture guide
  - **Codebase Cleanup**: Removed deprecated files, optimized imports, established clean documentation structure with comprehensive README index
  - **User Experience**: Consistent form behavior, validation, and navigation across all entity creation and editing workflows
  - **Architecture Benefits**: Single source of truth for form behavior, easy extensibility, simplified testing patterns, maintainable codebase
- **Schema Version Reset**: Reset all data model schemas to version 1 for clean development start
  - **Model Schema Reset**: Reset schema version fields to 1 in Account, Budget, Transaction, and UserProfile models
  - **Migration Logic Removal**: Removed BudgetMigrationService and all schema migration code from codebase
  - **Backward Compatibility**: Added schema version default handling in model fromJson methods for existing data
  - **Firestore Rules Update**: Updated security rules to remove schema version validation constraints
  - **Test Updates**: Updated all test files to use schema version 1 and removed migration-related tests
  - **Clean Development State**: Established clean slate for future schema versioning with version 1 baseline
  - **Technical Achievement**: All 554 tests passing with clean development environment ready for future schema evolution

- **Budget Period Management System**: Comprehensive budget period management system with precise period-based filtering, uniqueness constraints, and fallback logic
  - **Period-Based Architecture**: Added `periodStart` field to Budget model (schema v4) for exact period identification and filtering
  - **Uniqueness Constraints**: Enforced one budget per category per time period at both client and database level with atomic validation
  - **Fallback Logic**: Implemented `findLatestBudgetFromPreviousPeriod` method for displaying previous period budgets when none exist for current period
  - **Database Updates**: Updated Firestore security rules to validate new Budget model structure and added composite indexes for period-based queries
  - **Migration Support**: Created BudgetMigrationService to populate `periodStart` field for existing budgets based on `createdAt` and period type
  - **Enhanced Validation**: Updated BudgetValidators and repository validation to use precise period matching with `isForPeriod` method
  - **Provider Updates**: Enhanced `categoryBudgetInfo` provider with fallback logic and `isFallbackBudget` indicator for UI feedback
  - **Comprehensive Testing**: Added dedicated test suite for budget period management functionality
  - **Technical Achievement**: 524 tests passing with production-ready implementation ensuring robust period-specific budget management and data consistency

- **AppBar Architecture Reversal**: Comprehensive AppBar architecture reversal across entire BudApp from SliverAppBar to traditional Scaffold pattern
  - **Architecture Conversion**: Successfully converted all 9 screens from CustomScrollView + SliverAppBar to Scaffold + AppBar + SingleChildScrollView pattern
  - **AppBarHelpers Refactoring**: Updated all factory methods from SliverAppBar to regular AppBar creation with new method names (createTimePeriodScrollableAppBar, createStandardScrollableAppBar)
  - **Widget Pattern Migration**: Systematically converted SliverToBoxAdapter → direct widgets, SliverFillRemaining → Center/Expanded, SliverList → ListView.builder/Column, SliverPadding → Padding
  - **Overscroll Behavior Achievement**: Implemented desired overscroll behavior where both AppBar and content stretch together during overscroll
  - **Screen Coverage**: Converted Home, Transactions List, Accounts List, Categories List, Tags List, Budgets List, Transaction Create, Transaction Edit, and Transaction Detail screens
  - **Test Infrastructure Updates**: Fixed all test files to use new method names and updated test structure from sliver-based to regular AppBar testing patterns
  - **Quality Validation**: Flutter analyze clean (no issues found), all AppBarHelpers tests passing (10/10), proper code formatting maintained
  - **Technical Achievement**: 100% architectural consistency with traditional Scaffold pattern across entire application while maintaining all existing functionality

### Changed
- **Budget Model Schema**: Updated Budget model from schema version 3 to version 4 with `periodStart` field addition
- **Firestore Security Rules**: Enhanced budget validation functions to match current Budget model structure with new field validation
- **Firestore Indexes**: Added composite indexes for period-based budget queries and uniqueness constraint enforcement
- **Repository Validation**: Updated uniqueness validation to use precise period matching instead of approximate period checking
- **Provider Logic**: Enhanced budget providers to support fallback logic when no budget exists for current period

### Fixed
- **Period-Based Filtering**: Resolved issues with budget filtering by implementing exact `periodStart` date matching
- **Uniqueness Enforcement**: Fixed potential duplicate budget creation by adding database-level uniqueness constraints
- **Migration Compatibility**: Added migration support for existing budgets to populate missing `periodStart` field
  - **Material 3 Compliance**: Maintained Material 3 design compliance and TimePeriodSelector integration throughout the conversion process

- **Test Suite Maintenance - All Tests Passing**: Comprehensive resolution of failing tests with systematic TDD approach
  - **Mock Service Configuration**: Resolved mock service configuration issues in transaction-budget integration tests
  - **Fallback Value Registration**: Implemented proper fallback value registration for mocktail `any()` matchers using `registerFallbackValue(FakeTransaction())`
  - **Systematic Fix Pattern**: Established comprehensive pattern for BudgetTransactionService mock configuration with proper `when().thenAnswer()` setup
  - **Repository Test Fixes**: Successfully fixed all 5 repository test files with proper mock configurations and import corrections
  - **Transaction Creator Tests**: Fixed transaction creator tests with proper service integration and mock setup
  - **Account Balance Tests**: Resolved account balance update test failures with correct mock configuration
  - **Test Suite Health**: Achieved 100% test suite health with all 476 tests passing, zero failing tests
  - **Quality Maintenance**: Maintained Flutter analyze clean status and proper code formatting throughout fix process
  - **Technical Achievement**: Demonstrated systematic debugging approach using Sequential Thinking and established testing patterns

- **Budget Management Phase 4 - Transaction-Budget Integration**: Complete transaction-budget integration system with comprehensive service architecture
  - **Service Architecture**: Implemented comprehensive BudgetTransactionService with three core methods for transaction lifecycle management
  - **Atomic Operations**: Full support for both standalone operations and integration with existing Firestore transactions
  - **Provider Integration**: Added service provider with proper dependency injection and regenerated code using build_runner
  - **Constructor Updates**: Modified TransactionRepositoryImpl to accept BudgetTransactionService dependency for seamless integration
  - **Import Management**: Resolved namespace conflicts using import aliases (`as app_models`, `as firestore`) for clean code organization
  - **Type Safety**: Fixed all type casting issues and ensured proper Future return types throughout the integration
  - **Build Integration**: Successfully integrated with build_runner for clean code generation and dependency management
  - **Test Coverage**: All 476 tests passing with comprehensive coverage of transaction-budget integration scenarios

- **Budget Management Phase 3 - Edit Mode UI Implementation**: Comprehensive inline edit mode functionality for budget management system
  - **Edit Mode Toggle**: Implemented comprehensive edit mode state management with toggle functionality in app bar
  - **Simultaneous Editing**: Enabled editing of both total budget and individual category budgets in single interface
  - **UI State Management**: Added edit mode providers with proper state persistence and validation
  - **Form Integration**: Enhanced budget forms with inline editing capabilities and real-time validation
  - **User Experience**: Streamlined budget editing workflow with clear visual feedback and intuitive controls
  - **Technical Achievement**: Maintained all existing functionality while adding comprehensive edit mode capabilities
- **Unified Time-Period-Based Budget Management System**: Complete transformation of budget management with time-period integration and comprehensive bulk operations
  - **Global Time Period Integration**: Seamless integration with global time period selector for unified budget management experience
  - **Bulk Operations Backend**: Comprehensive BulkBudgetService with atomic operations for percentage adjustments, status changes, and batch deletion
  - **Bulk Operations UI**: Material 3 BulkBudgetOperationsDialog with form validation, error handling, and user feedback
  - **Selection Mode**: Multi-select capabilities with visual feedback, selection count display, and bulk action toolbar
  - **Enhanced Budget Screens**: Updated BudgetsListScreen with selection mode support and bulk operation integration
  - **Localization Support**: Comprehensive i18n strings for all bulk operations and user feedback messages
  - **Test Coverage**: All 476 tests passing with comprehensive coverage of bulk operations, transaction integration, and UI components
  - **Production Ready**: Complete unified budget management system ready for production use

### Changed
- **Budget Management Approach**: Transitioned from creation-based to edit-only budget management system (Phase 2)
  - **Removed Budget Creation UI**: Eliminated BudgetCreateScreen, FAB buttons, and "Set Budget" buttons from UI layer
  - **Removed Budget Creation Providers**: Cleaned up BudgetCreation Riverpod provider and createBudget methods
  - **Updated Navigation**: Removed budget creation routes while preserving budget list and edit functionality
  - **Enhanced Empty States**: Replaced creation prompts with informational guidance for upcoming features
  - **Documentation Updates**: Updated all documentation to reflect edit-only approach across BUDGET_MANAGEMENT.md, PRD.md, task files, and README.md
  - **Code Generation**: Successfully regenerated Riverpod providers with build_runner to remove creation dependencies
  - **Test Validation**: All 476 tests passing after budget creation removal, confirming system stability
- **Global Text Overflow System**: Comprehensive text overflow handling solution for consistent UI across the app
  - **Smart Text Widget (AppText)**: Context-aware text widget with automatic overflow configuration
  - **Context Detection**: Automatic detection of text context (title, body, label, note, etc.) based on style properties
  - **Predefined Configurations**: Ready-to-use configurations for different text contexts with appropriate maxLines and overflow settings
  - **Design Token Integration**: Seamlessly integrated with existing design tokens system in lib/config/design_tokens.dart
  - **Theme System Support**: Full compatibility with Material 3 theming and light/dark mode support
  - **Extension Methods**: Easy migration path from existing Text widgets through extension methods
  - **Adaptive Text Support**: Optional adaptive text scaling with FittedBox for responsive design
  - **Backward Compatibility**: Existing Text widgets continue to work while providing easy migration to AppText
  - **Comprehensive Testing**: Full test coverage including unit tests for configuration logic and widget tests for UI behavior
  - **Documentation**: Complete documentation with usage examples, migration guide, and architectural decisions
- **Time Period Modal Redesign**: Complete redesign of TimePeriodModal from list-based to modern grid-based interface
  - **Grid-Based Layout**: Replaced list interface with 3x4 month grid (Jan-Dec) for intuitive month selection
  - **Year Navigation**: Enhanced year selector with up/down arrow buttons and large year display
  - **Header Enhancement**: Gradient teal header with current period display and Material 3 styling
  - **Visual Feedback**: Circular selection indicators for selected months and current month dots
  - **Color Integration**: Maintained app's teal color scheme (#01A2A1) for brand consistency
  - **State Preservation**: Preserved all existing Riverpod state management and SharedPreferences persistence
  - **Functionality Maintained**: Kept period validation, future period prevention, and global time period state
  - **Accessibility**: Proper touch targets (64px height), semantic labels, and screen reader support
  - **Quality Assurance**: All tests passing, Flutter analyze clean, successful compilation and runtime verification
  - **User Experience**: Modern, touch-friendly interface aligned with contemporary mobile UI patterns
- **Navigation Refactoring Project**: Comprehensive navigation and UI structure refactoring with enhanced dashboard analytics
  - **Task 7 Complete**: Home Screen Dashboard Enhancement with four new analytics components
    - **Current Period Balance Card**: Monthly income/expense totals with navigation to transactions screen
    - **Recent Transactions Card**: Last 3 transactions display with "View All" navigation functionality
    - **Top Categories Analytics**: Top 3 expense and income categories with icons, names, and amounts
    - **Robust Icon Handling**: Support for both integer codes and string names for backward compatibility
    - **Technical Fixes**: Resolved Transaction model properties, deprecated Color methods, null safety issues
    - **Quality Assurance**: All 390 tests passing, Flutter analyze clean, proper code formatting
    - **App Runtime Verified**: Dashboard displays correctly without errors, all components functional
  - **Project Completion**: Successfully completed all 7 tasks including app drawer removal, navigation streamlining, and dashboard enhancement
  - **Modern Navigation**: Streamlined navigation patterns for improved user experience and accessibility
  - **Financial Insights**: Enhanced dashboard provides users with meaningful financial analytics and quick access to key information
- **Card UI Design Improvements**: Advanced card UI design improvements with account-specific iconography and streamlined layouts
  - **Account-Specific Icons**: Implemented account-specific iconography using `iconName` field with comprehensive icon mapping system
  - **Icon Mapping System**: Added 25+ financial and account-related icons including bank, investment, asset, and generic icons
  - **Clean Icon Styling**: Replaced container-based icons with large (48px) plain icons without background containers
  - **Visual Consistency**: Aligned account card styling with category card approach for unified design language
  - **Transaction Card Streamlining**: Removed bottom section with account/category information for cleaner, focused design
  - **Code Cleanup**: Eliminated unused methods (`_buildAccountInfo`, `_buildSingleAccountInfo`, `_buildCategoryInfo`)
  - **Functionality Preservation**: Maintained all existing features (navigation, notes, edit modals, menu actions)
  - **Quality Assurance**: All 390 tests passing, Flutter analyze clean, production-ready implementation
- **UI/UX Improvements**: Comprehensive UI/UX enhancements for improved user experience and cleaner interface design
  - **Account Card Navigation**: Modified account cards to navigate directly to filtered transaction views instead of account details
  - **Account Card Menus**: Added PopupMenuButton to account cards with complete menu options (edit, set primary, deactivate, delete)
  - **Screen Cleanup**: Removed account count summaries, welcome sections, and quick actions from home screen for cleaner interface
  - **Development Info Relocation**: Moved development information card from home screen to profile screen with enhanced Firebase testing
  - **Categories List Enhancement**: Added small spacing between tabs and category cards for better visual separation
  - **Navigation Enhancement**: Updated routing with `/transactions/account/:accountId` for account-specific transaction filtering
  - **Code Quality**: Maintained all 390+ tests passing, Flutter analyze clean, proper code organization
  - **User Experience**: Improved navigation flow, reduced visual clutter, enhanced accessibility and usability
- **Bottom Navigation Implementation**: Complete custom bottom navigation bar with Material 3 design and enhanced user experience
  - **Custom Navigation Bar**: CustomBottomNavigation with reduced height (64px), teal selection colors, outline icons
  - **Special Center Button**: CenterAddButton with pulse animation and smart test environment detection
  - **Toggle Functionality**: Categories/Budgets toggle with SharedPreferences persistence and seamless switching
  - **State Management**: Comprehensive Riverpod providers (bottomNavProviders) with navigation state tracking
  - **Router Integration**: Seamless GoRouter integration with route-based index detection and navigation handling
  - **Accessibility**: Haptic feedback, semantic labels, proper touch targets, screen reader compatibility
  - **Animation Handling**: Test environment detection to prevent pumpAndSettle timeout issues in tests
  - **UI Components**: CustomBottomNavigation, BottomNavItemWidget, CenterAddButton with Material 3 compliance
  - **Provider System**: bottomNavProviders with CategoryBudgetToggle and BottomNavIndex management
  - **Quality Assurance**: All 390+ tests passing, Flutter analyze clean, production-ready implementation
- **Task 14 Complete**: Enhanced budget editing and deletion functionality
  - Enhanced Firestore operations with atomic transactions and comprehensive error handling
  - Improved budget editing UI with real-time validation and better user feedback
  - Verified comprehensive soft deletion functionality is fully implemented
  - Confirmed real-time UI updates and progress indicators are working
  - Validated comprehensive Firestore Security Rules for budget operations
- **Tag Management System (Task 12)**: Complete tag management with CRUD operations and transaction associations
  - **Data Model**: Tag model with Freezed, JSON serialization, and Firestore integration
    - Implemented comprehensive Tag model with id, name, color, usageCount, schemaVersion, createdAt, updatedAt fields
    - Freezed implementation for immutability and type safety with JSON serialization support
    - Firestore integration with proper Timestamp handling and document conversion methods
    - Schema versioning support for future data migrations and backward compatibility
  - **Repository Pattern**: TagRepository with full CRUD operations and error handling
    - Created ITagRepository interface with comprehensive tag management methods
    - Implemented TagRepositoryImpl with Firestore backend integration and user isolation
    - Full CRUD operations: create, read, update, delete with proper validation and error handling
    - Real-time tag streaming with filtered providers for dynamic UI updates
  - **State Management**: Riverpod-based TagNotifier with AsyncNotifier pattern
    - TagNotifier with comprehensive state management for tag operations
    - AsyncNotifier pattern for proper async state handling and error propagation
    - Provider-based dependency injection for tag services and repositories
    - Real-time data streaming with automatic UI updates on tag changes
  - **UI Components**: TagsListScreen with search, TagCreateScreen, TagEditScreen with Material 3 design
    - TagsListScreen with search functionality and comprehensive tag display
    - TagCreateScreen for creating new tags with validation and color selection
    - TagEditScreen for modifying existing tags with pre-populated form data
    - Material 3 design compliance with proper theming and accessibility support
  - **Transaction Integration**: Many-to-many relationships using tagIds array in Transaction model
    - Enhanced Transaction model with tagIds field for tag associations
    - Many-to-many relationship implementation using array-based references
    - Transaction forms include tag selection with multi-select functionality
    - Tag management in transaction creation and editing workflows
  - **Cascading Deletion**: Automatic tag removal from transactions when tags are deleted
    - Implemented cascading deletion logic for data consistency
    - Automatic cleanup of tag references from associated transactions
    - Atomic operations ensuring transaction integrity during tag deletion
    - Proper error handling and rollback for failed deletion operations
  - **Navigation Integration**: Tags route in navigation drawer, FAB system integration
    - Added tags route to navigation drawer with proper menu item
    - FAB system integration for tag creation with Icons.local_offer
    - Proper routing with AppRoutes and go_router integration
    - Navigation drawer accessibility across all authenticated screens
  - **Security**: Comprehensive Firestore rules with user isolation and validation
    - Firestore Security Rules for tags collection with user data isolation
    - Field validation and business logic enforcement at database level
    - Proper authentication and authorization checks for tag operations
    - Data integrity validation and constraint enforcement
  - **Navigation Fix**: Resolved navigation drawer visibility by converting screens to CustomScrollView pattern
    - Fixed navigation drawer not visible on any screens issue
    - Converted all main screens from Scaffold + AppBar to CustomScrollView + SliverAppBar pattern
    - Maintained MainAppShell's navigation drawer accessibility across all screens
    - Preserved existing UI functionality while enabling navigation drawer access
  - **Quality**: All 390+ tests passing, Flutter analyze clean, proper code formatting
    - Comprehensive test coverage for all tag management features
    - Static analysis compliance with no issues found
    - Production-ready implementation with proper error handling
    - Code formatting and documentation standards maintained
- **Biometric Authentication System (Task 4)**: Complete biometric authentication with Face ID and Fingerprint support
  - **Hardware Detection**: BiometricService with availability checking and platform-specific error handling
    - Implemented comprehensive biometric availability detection using local_auth package
    - Platform-specific error handling with user-friendly error messages
    - Support for Face ID, Fingerprint, and other biometric authentication methods
    - Hardware and software capability detection with graceful fallbacks
  - **Authentication Flow**: Secure biometric authentication integrated with Firebase Auth
    - Seamless biometric authentication flow with Firebase Auth integration
    - Secure credential storage and retrieval using flutter_secure_storage
    - Biometric authentication as alternative to password-based login
    - Proper session management and authentication state handling
  - **Preference Management**: Biometric enable/disable with secure storage persistence
    - User preference management for biometric authentication settings
    - Secure storage of biometric preferences and authentication keys
    - Enable/disable functionality with proper cleanup and security measures
    - Settings integration for user control over biometric features
  - **Error Handling**: Comprehensive BiometricErrorService with user-friendly error messages
    - Specialized error handling service for biometric authentication errors
    - Platform-specific error code translation to user-friendly messages
    - BiometricErrorBanner component for consistent error display
    - Actionable error messages with guidance for resolution
  - **UI Components**: BiometricAuthButton, BiometricSettingsTile, BiometricErrorBanner
    - BiometricAuthButton for login screen integration with conditional rendering
    - BiometricSettingsTile for authentication settings management
    - BiometricErrorBanner for specialized biometric error display
    - Material 3 design compliance with proper theming and accessibility
  - **Settings Integration**: AuthSettingsScreen for biometric preference management
    - Dedicated authentication settings screen for biometric configuration
    - Toggle functionality with proper state management and error handling
    - Integration with existing profile and settings navigation flow
    - User-friendly interface for managing authentication preferences
  - **Platform Support**: Android and iOS permissions with proper configuration
    - Android permissions: USE_BIOMETRIC and USE_FINGERPRINT
    - iOS Face ID usage description in Info.plist
    - Platform-specific configuration for optimal biometric support
    - Proper permission handling and user guidance
  - **State Management**: Riverpod-based BiometricAuthNotifier with proper async state handling
    - BiometricAuthNotifier with comprehensive state management
    - Async state handling for authentication operations
    - Provider-based dependency injection for biometric services
  - **Biometric Authentication Gate**: App startup authentication gate for enhanced security
    - BiometricGateScreen for app startup biometric authentication
    - BiometricGateStateNotifier for session-based authentication tracking
    - Router integration with reactive authentication state management
    - Session-based gate logic that appears once per app session when enabled
    - Automatic biometric prompt with retry, settings, and sign out options
    - Reactive UI updates based on authentication state changes
  - **Quality Assurance**: All 390+ tests passing with Flutter analyze clean and zero compilation errors
    - Comprehensive test coverage for all biometric authentication features
    - Static analysis compliance with no issues found
    - Production-ready implementation with proper error handling
    - Code formatting and documentation standards maintained

- **User Profile Management and Account Recovery System (Task 3)**: Complete user profile management with Firebase Authentication integration
  - **Profile Editing**: Name and email updates with real-time validation and error handling
    - Implemented ProfileEditScreen with comprehensive form validation and user feedback
    - Real-time field validation with immediate error display and success messaging
    - Firebase Auth integration for display name updates and Firestore profile synchronization
    - Enhanced user experience with loading states and proper error recovery
  - **Password Management**: Secure password change flow with mandatory re-authentication
    - Created PasswordChangeScreen with multi-step security validation
    - Implemented re-authentication dialog with current password verification
    - Password strength indicator with real-time validation feedback
    - Secure password update using Firebase Auth with comprehensive error handling
  - **Account Recovery**: Password reset functionality using Firebase Auth email system
    - Implemented ForgotPasswordScreen with email validation and submission
    - Firebase Auth integration for password reset email delivery
    - User-friendly success messaging and error handling for all scenarios
    - Seamless integration with existing authentication flow
  - **Account Deletion**: Multi-step confirmation with re-authentication and comprehensive data cleanup
    - Created AccountDeletionDialog with progressive confirmation steps
    - Mandatory re-authentication before account deletion for security
    - Comprehensive data cleanup service for GDPR compliance
    - User data removal across all Firestore collections (accounts, transactions, categories, etc.)
    - Proper session invalidation and navigation after successful deletion
  - **Security Features**: Re-authentication dialogs, password strength indicators, and GDPR compliance
    - Reusable ReAuthenticationDialog component for sensitive operations
    - Password strength validation with visual feedback indicators
    - Comprehensive data cleanup service for complete user data removal
    - Security-first approach with mandatory re-authentication for sensitive operations
  - **UI Components**: Complete screen implementations with Material 3 design
    - ProfileEditScreen: Profile information editing with validation
    - PasswordChangeScreen: Secure password change workflow
    - ForgotPasswordScreen: Password reset email functionality
    - AccountDeletionDialog: Multi-step account deletion with confirmations
    - ReAuthenticationDialog: Reusable security component for sensitive operations
  - **Navigation Integration**: Seamless GoRouter integration with proper route handling
    - Complete route definitions for all profile management screens
    - Proper navigation flow with authentication guards and error handling
    - Back navigation handling with unsaved changes detection
    - Deep linking support for profile management features
  - **Repository Pattern**: Clean architecture with ProfileRepository and enhanced AuthRepository
    - ProfileRepository for user profile data management and Firestore operations
    - Enhanced AuthRepository with password management and account deletion methods
    - Proper separation of concerns with repository interfaces and implementations
    - Comprehensive error handling and validation at repository level
  - **Quality Assurance**: Zero static analysis issues with production-ready implementation
    - All Flutter analyze warnings resolved with clean code quality
    - Comprehensive error handling with user-friendly error messages
    - Proper async context handling and navigation safety
    - Production-ready implementation with comprehensive testing support
- **Transaction Editing and Deletion System (Task 11)**: Complete transaction management with enhanced user feedback
  - **Enhanced User Feedback System (Task 11.7)**: Comprehensive error handling and user experience improvements
    - Created centralized `TransactionErrorService` for consistent, user-friendly error messages
    - Enhanced delete operations with loading states, success messages, and comprehensive error handling
    - Added Firestore-specific error handling with appropriate retry mechanisms for network issues
    - Implemented consistent error handling patterns across transaction detail and list screens
    - Added comprehensive localization strings for all error scenarios and loading states
    - Enhanced both `TransactionDetailScreen` and `TransactionsListScreen` with improved delete confirmation flows
    - All 390+ tests passing with enhanced error handling integration
- **Atomic Transaction Deletion Implementation (Task 11.4)**: Complete Firestore transaction deletion operation with account balance reversals
  - **Atomic Deletion System**: Implemented comprehensive atomic transaction deletion functionality ensuring data consistency
    - Created `_calculateReverseBalanceAdjustments()` method for reversing transaction effects during deletion
    - Implemented balance reversal logic for all transaction types:
      - Income transactions: subtract from account balance (reverse the original addition)
      - Expense transactions: add back to account balance (reverse the original subtraction)
      - Transfer transactions: reverse both source and destination account adjustments
    - Enhanced `deleteTransaction()` method with atomic Firestore batch operations
    - Proper userId parameter for security and consistency with other repository methods
  - **Comprehensive Testing**: Created complete test suite with 6 test cases covering all scenarios
    - Income transaction deletion with balance reversal validation
    - Expense transaction deletion with balance reversal validation
    - Transfer transaction deletion with dual account balance reversal validation
    - Error handling for non-existent transactions and user validation
    - Atomic operation guarantees ensuring transaction deletion and balance updates occur together
    - All tests passing with proper error handling and edge case coverage
  - **UI Integration**: Enhanced transaction providers and UI components for deletion functionality
    - Updated `TransactionProviders` with proper userId parameter handling for deletion
    - Enhanced `TransactionDetailScreen` with delete confirmation dialog
    - Enhanced `TransactionsListScreen` with delete functionality and user feedback
    - Maintained existing UI patterns and user experience consistency
    - Added proper error handling and user feedback throughout deletion workflow
  - **Code Quality**: Achieved comprehensive quality standards
    - All 110 repository tests passing including new deletion functionality
    - Flutter analyze: "No issues found!" - Zero analysis issues
    - Dart format: All files properly formatted with no changes needed
    - Clean code architecture following established repository patterns
  - **ACHIEVEMENT**: Production-ready atomic transaction deletion with complete account balance consistency
- **Firebase Configuration Fix**: Critical Firebase deployment issue resolved
  - **Problem**: Firebase indexes and rules deployment was failing due to incorrect file location
  - **Root Cause**: `firestore.indexes.json` was located in `firebase/` subdirectory, but Firebase CLI requires it in project root
  - **Solution**: Moved `firestore.indexes.json` from `firebase/` directory to project root directory
  - **Impact**: Firebase deployment commands now work correctly for both indexes and rules
  - **Documentation Updates**:
    - Updated `README.md` with comprehensive Firebase deployment section
    - Added file location requirements and deployment command examples
    - Updated `firebase/README.md` with development workflow and critical location warnings
    - Added multi-environment deployment instructions
  - **Files Affected**:
    - `firestore.indexes.json`: Moved from `firebase/firestore.indexes.json` to `./firestore.indexes.json`
    - `README.md`: Added Firebase deployment section with file location requirements
    - `firebase/README.md`: Updated development workflow with deployment commands
  - **ACHIEVEMENT**: Firebase deployment infrastructure now works correctly across all environments
- **Transaction Edit Form/Modal Implementation (Task 11.2)**: Complete modal-based transaction editing functionality
  - TransactionEditModal Component:
    - Created comprehensive modal component with Material 3 styling and responsive design
    - Implemented DraggableScrollableSheet for optimal mobile user experience
    - Pre-populated form with existing transaction data for seamless editing
    - Integrated with existing TransactionForm widget for consistency and code reuse
    - Added proper keyboard handling, validation, loading states, and error handling
    - Supports editing of all transaction details: amount, category, date, description, accounts
  - Screen Integration:
    - Updated TransactionsListScreen to use modal instead of full-screen navigation
    - Updated TransactionDetailScreen with modal integration and fallback to full-screen edit
    - Added proper async context safety with context.mounted checks
    - Maintained backward compatibility while enhancing user experience
  - Comprehensive Testing:
    - Created complete test suite for TransactionEditModal component
    - Updated existing tests to reflect modal-based implementation
    - Fixed test expectations from navigation-based to modal-based behavior
    - All 378 tests passing with comprehensive coverage
  - Code Quality:
    - Resolved import conflicts using import aliases for better organization
    - Fixed callback signature mismatches and deprecated API usage
    - Updated to Material 3 compatible APIs (withValues instead of withOpacity)
    - Clean flutter analyze results and proper dart format compliance
  - **ACHIEVEMENT**: Complete transaction editing workflow with modal-based UI for improved user experience
- **Atomic Transaction Update Implementation (Task 11.3)**: Complete Firestore transaction update operation with account balance adjustments
  - **Atomic Update System**: Implemented comprehensive atomic transaction update functionality ensuring data consistency
    - Created `BalanceAdjustment` helper class for tracking account balance changes during updates
    - Implemented `_calculateBalanceAdjustments()` method handling all transaction types (income, expense, transfer)
    - Added `_validateAccountsForUpdate()` method for comprehensive account validation before updates
    - Created `updateTransactionWithBalanceAdjustment()` method for atomic Firestore batch operations
    - Updated existing `updateTransaction()` method to use new atomic implementation for seamless integration
  - **Balance Calculation Logic**: Comprehensive algorithms for all transaction scenarios
    - **Income Transactions**: Reverse old amount from old account, apply new amount to new account
    - **Expense Transactions**: Add back old amount to old account, subtract new amount from new account
    - **Transfer Transactions**: Reverse old transfer completely, apply new transfer (handles up to 4 account changes)
    - **Account Changes**: Proper handling when users change accounts during transaction edits
  - **Data Consistency Guarantees**: Firestore transaction-based atomic operations
    - All document changes (transaction + account balances) succeed or fail together
    - Prevents partial updates that could leave accounts in inconsistent states
    - Proper error handling and rollback for failed operations
  - **Comprehensive Testing**: Created extensive test suite for atomic update functionality
    - Test coverage for all transaction types and account change scenarios
    - Error handling validation for non-existent transactions and accounts
    - Balance adjustment verification for complex transaction modifications
    - Integration testing with existing transaction editing UI components
  - **Quality Assurance**: All 378 tests passing, Flutter analyze clean, seamless UI integration
  - **ACHIEVEMENT**: Production-ready atomic transaction updates with complete account balance consistency
- **Transaction Action UI Implementation (Task 11.1)**: Complete user interface for transaction editing and deletion actions
  - Enhanced TransactionCard widget with action button functionality:
    - Added showActions, onEdit, and onDelete parameters for flexible action display
    - Implemented PopupMenuButton with edit/delete menu items using Material 3 design
    - Maintained backward compatibility with existing TransactionCard usage
  - TransactionsListScreen integration with comprehensive action handling:
    - Added _handleEditTransaction method for navigation to edit screen (/transactions/:id/edit)
    - Added _handleDeleteTransaction method with confirmation dialog and deletion logic
    - Integrated showActions: true parameter for action button visibility
    - Proper error handling and success messaging with localized feedback
  - Localization enhancement:
    - Added "transactionDeleted" key to app_en.arb for success messaging
    - Consistent with existing localization patterns for user feedback
  - Comprehensive testing infrastructure:
    - Created 6 unit tests for TransactionCard action functionality (all passing)
    - Created 3 integration tests for action workflow (visibility, edit navigation, delete confirmation)
    - Fixed provider override patterns using Stream.value() for StreamProvider mocking
    - Resolved navigation test expectations to check for successful navigation rather than exact text matching
  - Code quality achievements:
    - Fixed 8 flutter analyze warnings by converting relative imports to absolute package imports
    - All 371 tests passing with comprehensive coverage of action functionality
    - Clean flutter analyze results with "No issues found!"
    - Proper dart format compliance with no changes needed
  - **ACHIEVEMENT**: Complete transaction action UI foundation ready for Task 11.2 (Transaction Edit Form/Modal)
- **Client-Side Logic for Transfer Account Updates (Task 10.9)**: Completed atomic balance updates for ALL transaction types with transfer transaction implementation
  - Transfer Transaction Balance Updates:
    - Enhanced TransactionRepositoryImpl.createTransferTransaction() method with dual account balance updates
    - Implemented atomic Firestore transaction pattern to update both source and destination account balances
    - Source account balance decreases by transfer amount, destination account balance increases by transfer amount
    - Used _firestoreService.runTransaction() for atomic operations ensuring data consistency across both accounts
    - Added proper error handling for non-existent accounts and same-account transfer validation
    - Maintained consistency with existing income/expense transaction patterns for code maintainability
  - Comprehensive Testing Infrastructure:
    - Added 5 new test cases in test/data/repositories/transaction_balance_update_test.dart
    - Test coverage includes: single transfers, multiple transfers, negative balances, mixed transaction types, error handling
    - All tests verify both transaction creation AND account balance updates for complete validation
    - Uses FakeFirebaseFirestore and MockDataFactory for reliable test data management
    - Total test count increased from 10 to 15 tests in balance update test file, all passing
  - Code Quality: All 351 tests passing, Flutter analyze "No issues found!", dart format applied, comprehensive error handling
  - **ACHIEVEMENT**: Complete atomic balance update implementation for ALL transaction types (income, expense, transfer) with data consistency guarantees

### Fixed
- **Code Quality**: Fixed deprecation warnings for `withOpacity()` method, replaced with `withValues(alpha:)` in error display widgets
- **CRITICAL: Current Balance Display Fix**: Resolved critical UI issue where application displayed initial balances instead of current balances throughout the interface
  - **Problem**: Application was incorrectly showing `initialBalanceCents` (starting balance) instead of `currentBalanceCents` (real-time balance) across all UI components
  - **Root Cause**: UI components were using the wrong balance field, showing outdated starting balances instead of real-time current balances updated by transactions
  - **Solution**: Systematically updated 7 files across presentation layer, providers, and repository implementations
    - Account cards, detail screens, transaction dropdowns now display `currentBalanceCents`
    - Summary calculations (total assets, liabilities, net worth) use current balances
    - Account edit screen UX improved with clear labeling and explanatory notes
    - Repository methods return current balances for user-facing operations
  - **Files Modified**: account_card.dart, account_detail_screen.dart, account_selector.dart, accounts_list_screen.dart, account_providers.dart, account_repository_impl.dart, account_edit_screen.dart
  - **Quality Assurance**: All 362 tests passing, `flutter analyze` clean, `dart format` applied
  - **Impact**: Users now see accurate real-time financial status throughout the application
- **CRITICAL: Account Balance Update Bug Fix**: Resolved critical issue where account balances were not updating when transactions were created via UI
  - **Problem**: Account balances remained unchanged when users created transactions through the transaction creation screen
  - **Root Cause**: UI layer called generic `TransactionCreator.createTransaction()` method which used `transactionRepository.createTransaction()` that does NOT update account balances, instead of the type-specific methods that DO include atomic balance updates
  - **Solution**: Modified `TransactionCreator.createTransaction()` to route to appropriate type-specific repository methods based on transaction type:
    - `TransactionType.income` → `createIncomeTransaction()` (adds to account balance atomically)
    - `TransactionType.expense` → `createExpenseTransaction()` (subtracts from account balance atomically)
    - `TransactionType.transfer` → `createTransferTransaction()` (updates both account balances atomically)
  - **TDD Approach**: Created comprehensive failing tests first to demonstrate the bug, then implemented minimal fix to make tests pass
  - **Files Modified**:
    - `lib/features/transactions/providers/transaction_providers.dart`: Enhanced `createTransaction()` method with type-specific routing
    - `test/features/transactions/account_balance_update_test.dart`: Created comprehensive TDD test suite (4 test cases)
  - **Validation**: All 362 tests passing, Flutter analyze clean, dart format applied
  - **Impact**: Transaction creation now properly updates account balances - users see real-time balance changes when creating transactions
- **CRITICAL: Create Transaction Button Fix**: Resolved critical issue where "Create Transaction" button remained greyed out and non-functional
  - **Problem**: Button stayed disabled even after filling all required fields, preventing transaction creation
  - **Root Cause**: Amount field used TextEditingController but form state wasn't updated in real-time
  - **Solution**: Added real-time form state updates via onChanged callback in AmountInputField
  - **Implementation**: Created _handleAmountChanged method to convert text input to cents and update form state immediately
  - **Testing**: Used Test-Driven Development (TDD) approach - created tests first to demonstrate problem, then implemented fix
  - **Files Modified**:
    - lib/features/transactions/presentation/widgets/amount_input_field.dart: Added onChanged parameter
    - lib/features/transactions/presentation/widgets/transaction_form.dart: Added real-time amount update logic
    - test/features/transactions/button_state_test.dart: Created comprehensive button state validation tests
  - **Impact**: Transaction creation now works properly - users can successfully create transactions
  - **Quality**: All 358 tests passing including new button state validation tests
- **Account Selector Overflow Fix**: Resolved 12-pixel RenderFlex overflow in account dropdown menu
  - Changed account dropdown items from multi-line to single-line layout
  - Account name and balance now display on one line: "Account Name - USD 1000.00"
  - Fixed infinite loop semantics assertion error caused by complex nested layout constraints
  - Account selection now works properly without layout exceptions
  - Updated tests to match new single-line account display format
  - All transaction creation screen tests now pass without layout issues

- **Client-Side Logic for Income/Expense Account Updates (Task 10.8)**: Implemented atomic balance updates for income and expense transactions
  - Account Model Enhancement:
    - Added currentBalanceCents field to Account model for stored balance tracking alongside existing initialBalanceCents
    - Added backward compatibility in Account.fromJson() to handle existing Firestore data without currentBalanceCents field
    - Updated all test files and mock data factories to include the new required parameter
    - Ran dart run build_runner build --delete-conflicting-outputs to regenerate Freezed files
  - Atomic Transaction Implementation:
    - Enhanced createIncomeTransaction() method with atomic balance updates (income adds to account balance)
    - Enhanced createExpenseTransaction() method with atomic balance updates (expense subtracts from account balance)
    - Used _firestoreService.runTransaction() for atomic operations ensuring data consistency and preventing race conditions
    - Added proper error handling for non-existent accounts and validation failures
    - Maintained existing account validation and business rule enforcement from Task 10.7
  - Account Creation Logic:
    - Modified AccountCreateScreen to set currentBalanceCents equal to initialBalanceCents for new accounts
    - Updated MockDataFactory to properly initialize both balance fields in test data
    - Maintained backward compatibility for existing account data in Firestore
  - Comprehensive Testing Infrastructure:
    - Created test/data/repositories/transaction_balance_update_test.dart with 8 comprehensive test cases
    - Tests cover income transactions (balance increases), expense transactions (balance decreases), mixed scenarios, and error handling
    - All 344 tests passing including new balance update test suite and existing transaction functionality
    - Fixed import path issues using package imports for reliable test execution
  - Code Quality: Flutter analyze "No issues found!", dart format applied to 7 files, comprehensive test coverage
- **Firestore Repository for Transaction Creation (Task 10.7)**: Enhanced transaction repository with account validation and type-specific creation methods
  - Enhanced TransactionRepositoryImpl with comprehensive account existence validation:
    - Added _validateAccountExists() helper method for Firestore account validation
    - Enhanced validateTransaction() method with account existence checks ensuring referenced accounts exist and belong to correct user
    - Added proper error handling with descriptive ArgumentError messages for validation failures
  - Implemented three transaction type-specific creation methods:
    - createIncomeTransaction(): Validates destination account existence and user ownership
    - createExpenseTransaction(): Validates source account existence and user ownership
    - createTransferTransaction(): Validates both accounts exist, belong to user, and are different accounts
    - Each method enforces proper transaction type requirements and business rules
    - Returns transaction ID for successful operations, throws ArgumentError for validation failures
  - Enhanced TransactionCreator provider with transaction type-specific methods:
    - Added createIncomeTransaction(), createExpenseTransaction(), createTransferTransaction() methods to provider
    - Proper state management with loading/error states using AsyncValue pattern
    - Enhanced error handling with transaction ID return values for successful operations
    - Integration with repository layer for consistent validation and data persistence
  - Comprehensive testing infrastructure:
    - Created test/data/repositories/transaction_repository_creation_test.dart with 10 test cases
    - Created test/features/transactions/providers/transaction_creator_test.dart with 9 test cases
    - Tests cover all three transaction types with success and error scenarios
    - Account validation testing with FakeFirebaseFirestore integration
    - Provider-level testing with ProviderContainer and overrides for state management validation
  - Code generation and quality maintenance:
    - Ran dart run build_runner build --delete-conflicting-outputs to regenerate Riverpod providers
    - All 336 tests passing including new transaction creation test suites
    - Flutter analyze: "No issues found!" - Perfect code quality maintained
    - Dart format: All files properly formatted with no changes needed
- **Client-Side Validation for Transaction Forms (Task 10.6)**: Comprehensive validation system with localized error messages and real-time feedback
  - Implemented comprehensive TransactionValidators service:
    - Amount validation (required, numeric, positive values, decimal precision)
    - Account validation (required selection, valid account references)
    - Date validation (required, valid date format, reasonable date ranges)
    - Description validation (optional, length limits, character restrictions)
    - Notes validation (optional, length limits for additional details)
    - Currency validation (ISO 4217 format, supported currencies)
  - Enhanced transfer-specific validation:
    - Source and destination account selection validation
    - Business rule enforcement (source ≠ destination accounts)
    - Dual account validation with proper error messaging
    - Integration with existing transfer transaction UI components
  - Comprehensive localization integration:
    - Added 15+ new validation error strings to app_en.arb
    - Context-aware error messages for better user experience
    - Consistent with existing authentication validation patterns
    - Full integration with AppLocalizations system
  - Complete form state integration:
    - Updated TransactionFormProviders to use new validation service
    - Real-time validation with immediate user feedback
    - Form-level validation ensuring all required fields are complete
    - Enhanced validateForm(BuildContext context) method with localized messages
  - Testing infrastructure improvements:
    - Fixed Flutter analyze warnings about deprecated validateFormLegacy() method usage
    - Updated test files to use new validateForm(BuildContext context) method
    - Added proper BuildContext capture pattern in tests using testWidgets
    - Fixed pending timer issues in tests with await tester.pumpAndSettle()
    - All transaction form tests passing with proper async cleanup
  - Code quality achievements:
    - Flutter analyze: "No issues found!" - Perfect code quality achieved
    - All test files updated to use current validation methods
    - Proper imports and BuildContext handling in test infrastructure
    - Consistent testing patterns across transaction validation tests
- **Transaction UI Components Implementation (Task 10.3)**: Complete transaction form UI components with comprehensive validation and state management
  - Implemented complete transaction form system:
    - TransactionForm main widget with comprehensive validation and form submission logic
    - TransactionTypeSelector for income/expense/transfer type selection with SegmentedButton
    - AmountInputField with currency support, real-time formatting, and proper validation
    - AccountSelector with dynamic labeling based on transaction type and proper account filtering
    - CategorySelector with type-based filtering and hierarchical category support
    - DatePickerField for transaction date selection with Material 3 date picker
    - DescriptionField and NotesField for transaction details with character limits
  - Complete state management integration with Riverpod:
    - TransactionFormState class with comprehensive form state management including transactionType property
    - TransactionForm provider with validation and transaction building logic
    - CRUD providers for transaction operations (TransactionCreator, TransactionUpdater, TransactionDeleter)
    - Real-time validation with field-level error display and AsyncValue patterns
  - Comprehensive localization support:
    - Added 40+ transaction-specific localization strings to app_en.arb
    - Complete coverage for all form components with validation messages, success/error messages, placeholders
    - Consistent with existing authentication and account localization patterns
    - Generated localization files updated successfully
  - Screen implementation and navigation:
    - TransactionCreateScreen for hosting the transaction form with proper navigation integration
    - Success/error handling with user-friendly SnackBar notifications
    - Integration with app routing system and authentication guards
    - Material 3 design compliance with BudApp design tokens
  - Code generation and quality assurance:
    - Successfully generated all required provider files using build_runner
    - All 307 tests passing with no compilation errors or analyzer issues
    - Flutter analyze: "No issues found!" - Clean code quality maintained
    - Proper error handling and edge case coverage throughout implementation
- **Transaction Database Schema Definition (Task 10.2)**: Comprehensive database schema documentation and optimization
  - Created comprehensive Transaction Database Schema documentation (docs/TRANSACTION_DATABASE_SCHEMA.md):
    - Complete field specifications with data types, constraints, and validation rules
    - Transaction type business logic for income, expense, and transfer transactions
    - Account reference requirements and validation for each transaction type
    - Data examples with valid/invalid transaction documents
    - Security rules documentation and implementation status
  - Added 8 optimized Firestore composite indexes to firebase/firestore.indexes.json:
    - Primary index: userId + transactionDate (descending) for chronological queries
    - Type filtering: userId + type + transactionDate for transaction type queries
    - Status filtering: userId + status + transactionDate for status-based queries
    - Category filtering: userId + categoryId + transactionDate for category analysis
    - Account filtering: userId + fromAccountId/toAccountId + transactionDate for account queries
    - Tag filtering: userId + tags + transactionDate for tag-based searches
    - Amount sorting: userId + amountCents for amount-based analysis
  - Created visual schema diagrams with Mermaid:
    - Entity Relationship Diagram showing data relationships between users, accounts, transactions, and categories
    - Transaction Type Business Logic flowchart illustrating validation rules and money flow
  - Documented query patterns and performance considerations:
    - Pagination strategies with limit() and startAfter() for large datasets
    - Date range queries with compound filters for efficient time-based analysis
    - Account-based filtering with Filter.or() for comprehensive account queries
    - Cost optimization guidelines and scalability patterns
  - Enhanced collection structure documentation:
    - Firestore path: /users/{userId}/transactions/{transactionId} for user isolation
    - Field validation rules matching existing Transaction model implementation
    - Security rules integration with business logic validation
    - Migration considerations and schema versioning support
  - Implementation status tracking:
    - Completed: Transaction model, repository interface/implementation, security rules, CRUD operations
    - Newly added: Comprehensive schema documentation, optimized indexes, visual diagrams
    - Ready for: Transaction UI implementation with complete database foundation
- **Firestore Collection Structure for Categories & Subcategories (Task 9.10)**: Complete Firestore integration with proper data conversion methods
  - Enhanced Category model with `fromFirestore()` and `toFirestore()` methods:
    - Proper handling of Firestore Timestamp conversion to/from DateTime
    - Document ID consistency enforcement in fromFirestore method
    - Automatic data type conversion for Firestore storage
    - Support for all Category fields including metadata and hierarchical relationships
  - Created comprehensive Firestore collection structure documentation:
    - Detailed document schema with field definitions and constraints
    - Collection path structure: `users/{userId}/categories/{categoryId}`
    - Query patterns and composite index requirements
    - Security rules implementation guidelines
    - Data type conversion specifications for Firestore integration
  - Added comprehensive test suite for Firestore integration:
    - Unit tests for `toFirestore()` method with Timestamp conversion validation
    - Round-trip conversion testing through JSON serialization
    - Data type validation for enum serialization and metadata handling
    - Proper handling of null optional fields and default values
  - All tests passing (307 tests) with no analyzer issues and proper code formatting
- **Frontend UI for Guided Deletion & Re-assignment Flow (Task 9.9)**: Complete user interface for category deletion with constraint handling
  - Created CategoryDeletionDialog with guided deletion workflow:
    - Automatic constraint checking using CategoryDeletionService
    - Display of associated transactions and subcategories
    - Options for transaction re-assignment before deletion
    - Simple confirmation for unconstrained deletions
    - Comprehensive error handling and user feedback
  - Implemented CategoryReassignmentDialog for target category selection:
    - Filtered category list excluding current category and matching type
    - Real-time category data with Riverpod integration
    - User-friendly category selection interface
  - Enhanced CategoriesListScreen with guided deletion integration:
    - Updated CategoryTreeWidget to handle menu actions
    - Integrated guided deletion flow with existing category cards
    - Proper provider invalidation for UI refresh after operations
  - Added comprehensive localization support (15+ new strings):
    - Deletion constraint messages and confirmation dialogs
    - Transaction re-assignment workflow text
    - Error messages and success notifications
    - User guidance for complex deletion scenarios
  - Enhanced CategoryDeletionService provider integration:
    - Proper dependency injection with category and transaction repositories
    - Provider-based state management for deletion operations
    - Seamless integration with existing category management system
  - Complete UI workflow implementation:
    - Constraint checking → Display conflicts → Re-assignment options → Confirmation → Deletion
    - Loading states and progress indicators throughout the process
    - Proper error handling with user-friendly messages
    - Success feedback with automatic UI refresh
  - All tests passing (301 tests) with no analyzer issues and proper code formatting
- **Transaction Re-assignment Pre-deletion Implementation (Task 9.8)**: Complete client-side logic for transaction re-assignment before category deletion
  - Extended CategoryDeletionService with comprehensive transaction re-assignment functionality:
    - `getTransactionsForCategory()` - Retrieves all transactions associated with a specific category
    - `validateReassignmentTarget()` - Validates new category for re-assignment (same type, active, same user)
    - `reassignCategoryTransactions()` - Performs bulk transaction re-assignment with comprehensive validation
    - `deleteWithReassignment()` - Combined re-assignment and deletion operation for seamless workflow
    - `deleteSubcategoryWithReassignment()` - Subcategory-specific re-assignment and deletion operation
  - Created CategoryReassignmentResult class for comprehensive operation results
    - Success/error status with detailed error messages and transaction counts
    - User-friendly success messages for UI feedback with affected transaction information
    - Category name tracking for better user experience and operation transparency
  - Enhanced repository integration leveraging existing TransactionRepository methods
    - Uses `reassignTransactionsToCategoryForUser()` for efficient bulk updates with proper user context
    - Maintains transaction integrity during re-assignment with proper error handling
    - Added missing interface method to ITransactionRepository for user-scoped operations
  - Comprehensive validation for re-assignment targets ensuring data integrity:
    - Same CategoryType validation (income/expense) to prevent type mismatches
    - Active status verification to ensure target category is available
    - User ownership validation to maintain data isolation
    - Self-assignment prevention to avoid circular references
    - Existence verification to ensure target category is valid
  - Complete test coverage with 100% pass rate (9 test cases covering all scenarios)
    - Unit tests for all re-assignment scenarios including edge cases
    - Validation testing for all constraint types and error conditions
    - Error handling and recovery testing with proper exception management
    - Integration testing with existing deletion constraints and repository methods
  - Updated CategoryDeletionService provider with proper dependency injection for transaction repository
  - Maintained offline-first architecture principles and repository pattern consistency
  - All tests passing (301 tests) with no analyzer issues and proper code formatting
- **Category Deletion Constraints Implementation (Task 9.7)**: Complete client-side validation and Firestore Security Rules for safe category deletion
  - Enhanced CategoryRepositoryImpl with comprehensive constraint checking methods:
    - `hasDependentTransactionsForUser()` - Validates no associated transactions exist before deletion
    - `hasChildSubcategories()` - Ensures no active child subcategories prevent deletion
    - `deleteCategoryWithConstraints()` - Safe category deletion with full validation pipeline
    - `deleteSubcategoryWithConstraints()` - Subcategory deletion with hierarchy integrity checks
    - `deactivateCategoryWithUserId()` - Soft deletion with proper user context validation
  - Created CategoryDeletionService for centralized deletion logic with structured error handling
    - CategoryDeletionResult class for consistent error response patterns
    - User-friendly error messages explaining deletion failures and required actions
    - Support for both categories and subcategories with comprehensive validation
  - Enhanced Firestore Security Rules with server-side deletion constraints
    - Updated `canDeleteCategory()` function to enforce custom-only deletion (predefined categories protected)
    - Added requirement for category deactivation before deletion
    - Comprehensive documentation of app-level validation requirements and limitations
  - Added localized error messages for all deletion constraint scenarios
    - Specific messages for transaction dependency conflicts
    - Clear guidance for subcategory hierarchy conflicts
    - Proper error context for improved user experience
  - Updated ICategoryRepository interface with all new constraint checking methods
  - Maintained offline-first architecture principles and repository pattern consistency
  - All tests passing (292 tests) with no analyzer issues
- **Category Creation/Editing UI Implementation (Task 9.6)**: Complete CRUD interface for category management
  - Created CategoryValidators service with comprehensive client-side validation and localized error messages
  - Implemented CategoryCreateScreen for new category creation with type selection and customization
  - Built CategoryEditScreen for existing category modification with form pre-population
  - Added SubcategoryCreateScreen for subcategory creation with parent category selection
  - Created SubcategoryEditScreen for subcategory modification with hierarchy validation
  - Developed reusable form components: CategoryTypeSelector, CategoryColorSelector, CategoryIconSelector, ParentCategorySelector
  - Integrated navigation with floating action button and modal bottom sheet for creation options
  - Implemented Material 3 design compliance with proper theming and accessibility support
  - Added real-time validation with field-level error display and immediate feedback
  - Created success/error handling with user-friendly SnackBar notifications
  - Updated routing configuration for all category CRUD operations (/categories/create, /categories/:id/edit, etc.)
  - Fixed all Flutter analyzer issues (20 deprecation warnings resolved)
  - Applied proper code formatting and maintained test coverage
  - Added comprehensive localization strings for form validation and user feedback
- **Category Frontend UI Implementation (Task 9.5)**: Complete category management interface with hierarchical display
  - Created CategoriesListScreen with tree view display for parent-child category relationships
  - Implemented CategoryTreeWidget with expandable/collapsible categories and visual connection lines
  - Built CategoryCard component with type indicators, status badges, and action menus
  - Added CategoryTypeFilter bottom sheet for advanced filtering by type, source, and status
  - Created EmptyCategoriesState with user-friendly onboarding for first-time category creation
  - Integrated categories into bottom navigation with proper routing (/categories, /categories/:id)
  - Added comprehensive localization with 50+ category-specific strings in app_en.arb
  - Implemented summary dashboard showing category statistics by type and source
  - Created comprehensive widget test suite with 6 test cases covering all UI states
  - All tests passing with proper provider mocking and error handling
  - Material 3 design compliance with dark/light theme support and accessibility features
- **Category Management System Implementation**: Complete hierarchical category system for transaction organization
  - Created Category data model with Freezed implementation for immutability
  - Implemented CategoryType enum (income/expense) and CategorySource enum (custom/predefined)
  - Added hierarchical category support with parent-child relationships
  - Built ICategoryRepository interface with complete CRUD operations
  - Implemented CategoryRepositoryImpl with Firestore backend integration
  - Created comprehensive Riverpod provider architecture for category state management
  - Added real-time category data streaming with filtered providers
  - Implemented category validation logic and business rule enforcement
  - Added name uniqueness checking and user category count tracking
  - Created comprehensive test suite with model, repository, and provider tests
  - All 278 tests passing with proper error handling and validation
  - Documentation created in docs/CATEGORY_MANAGEMENT.md with usage examples
- **Subcategory CRUD Operations Implementation (Task 9.4)**: Complete client-side Firestore operations for subcategories
  - Enhanced ICategoryRepository interface with 11 new subcategory-specific methods
  - Implemented subcategory creation with parent validation and hierarchy depth limits (max 3 levels)
  - Added subcategory updates with type consistency and name uniqueness validation
  - Implemented subcategory deletion with dependency checks and soft delete support
  - Created subcategory hierarchy validation to prevent circular references
  - Added real-time subcategory streams and count providers with family providers
  - Implemented SubcategoryCreateNotifier, SubcategoryUpdateNotifier, and SubcategoryValidationNotifier
  - Added business rule enforcement: type consistency, depth limits, name uniqueness within parent
  - Created comprehensive test suite with 7 test cases covering model validation and business logic
  - All subcategory operations maintain data integrity and follow offline-first architecture
- **Account Reading & Display Implementation (Task 6.6)**: Complete account detail viewing functionality
  - Created AccountDetailScreen with comprehensive account information display
  - Real-time data streaming with StreamProvider-based architecture for live updates
  - Account navigation integration with `/accounts/:id` routing pattern  
  - User action menu system (edit, set primary, deactivate, delete) with confirmation dialogs
  - Added 40+ new localized strings for complete i18n coverage
  - Comprehensive test suite with 5 test cases covering all UI states
  - Fixed UI overflow issues in popup menus with responsive layout fixes
  - Updated IAccountRepository interface to include watchAccountForUser method
  - Enhanced account providers for real-time financial statistics and filtered views
- **Feature-Based Architecture Restructuring**: Complete project reorganization for improved maintainability
  - Migrated from technical layer-based to feature-based directory structure
  - Created dedicated feature modules: auth, accounts, transactions, budgets, goals, dashboard
  - Each feature contains presentation/, services/, and providers/ subdirectories
  - Moved home_screen.dart to features/dashboard/presentation/screens/
  - Reorganized test structure to mirror lib/ organization
  - Updated all import paths and maintained 100% test coverage (183 tests passing)
  - Cleaned up global directories to contain only truly shared components
  - Moved session_state.dart to data/models/ for proper data layer organization
  - Removed unused files and empty directories
  - All builds successful across dev/staging/prod flavors
  - Perfect code quality maintained: Flutter analyze reports "No issues found!"
- **Repository Pattern Refinement**: Removed problematic IBaseRepository abstraction
  - Eliminated IBaseRepository interface that violated Interface Segregation Principle
  - Updated all repository interfaces to be standalone (IUserRepository, IAccountRepository, ITransactionRepository, etc.)
  - Removed @override annotations from methods that threw UnimplementedError
  - Added proper @override annotations only to methods that actually implement interface contracts
  - Achieved perfect code quality: Flutter analyze reports "No issues found!"
  - Repository pattern now aligns better with user-scoped data architecture
- **Complete Localization Infrastructure**: Implemented flutter_gen_l10n for internationalization support
  - Added flutter_localizations and intl dependencies
  - Created comprehensive app_en.arb with 40+ localized strings
  - Configured l10n.yaml and pubspec.yaml for automatic code generation
  - Updated main.dart with localization delegates and supported locales
- **Centralized UI Strings**: Replaced all hardcoded strings with AppLocalizations
  - Updated all auth screens (login, signup, forgot password, email verification)
  - Localized home screen and form validation messages
  - Enhanced AuthValidators to accept BuildContext for localized error messages
  - Established single source of truth for all user-facing text
- **Firebase Service Architecture Refactoring**: Replaced static service pattern with proper dependency injection
  - Created FirebaseInitializationService with injectable dependencies
  - Implemented firebase_providers.dart with Riverpod provider pattern
  - Removed static FirebaseService class in favor of testable architecture
  - Updated all Firebase initialization logic to use providers
  - Enhanced test coverage with proper mocking capabilities
- **Architectural Refactoring Phase 1**: Complete migration to Riverpod + Repository pattern
  - **Riverpod Infrastructure**: Comprehensive provider structure with auth, UI, Firebase, and repository providers
  - **Repository Pattern**: Complete interface hierarchy with base repository and entity-specific interfaces
  - **Data Models**: Immutable data models using freezed with JSON serialization for Firestore
  - **UserRepository Implementation**: Complete concrete repository with Firestore backend and comprehensive testing
  - **Authentication UI Migration**: All auth screens converted to ConsumerStatefulWidget with reactive state management
  - **AuthService Refactoring**: Converted from static to instance-based with dependency injection via Riverpod providers
    - Constructor dependency injection for FirebaseAuth, GoogleSignIn, and IUserRepository
    - Automatic user profile creation/updates via UserRepository integration
    - Enhanced testability with mocked dependencies and isolated testing
    - Simplified UI authentication flow with automatic user management
  - **FirestoreService Implementation**: Separate injectable service with comprehensive Firestore operations
    - Centralized Firestore access with dependency injection pattern
    - Common operations (collections, batches, transactions, connectivity testing)
    - Enhanced testability and clean separation from Firebase SDK
  - **Complete Repository Implementation**: Transaction and Account repositories with full functionality
    - **TransactionRepositoryImpl**: Comprehensive transaction management with user-scoped data access
      - Transaction statistics, analytics, and search capabilities
      - Transaction validation, bulk operations, and category/account reassignment
      - User-isolated data storage with proper Firestore sub-collections
    - **AccountRepositoryImpl**: Complete account management with user-scoped operations
      - Account validation, uniqueness checking, and primary account management
      - Account statistics, balance calculations, and search functionality
      - Account lifecycle management (activate/deactivate/delete)
    - **Provider Integration**: transactionRepositoryProvider and accountRepositoryProvider with proper dependency injection
  - **Testing Infrastructure**: Enhanced testing with provider overrides, mocked dependencies, and isolated testing
- **Session Management System**: Complete SessionService with user preferences, analytics, and state management
- **Error Handling System**: Centralized AuthErrorService with contextual messaging and retry functionality
- **Firebase Emulator Testing Setup**: Complete Firebase emulator configuration for local testing
  - Firebase Auth emulator on port 9099
  - Firestore emulator on port 8080
  - Emulator UI on port 4000
  - Automated emulator startup scripts
- **Comprehensive Widget Tests**: Email verification screen UI testing without Firebase dependency
  - Material 3 styling validation
  - Button interaction testing
  - Component layout verification
- **Testing Infrastructure**: Enhanced testing setup and documentation
  - Firebase test helper utilities
  - Emulator integration scripts
- **Freezed Data Models**: Complete migration to immutable data structures
  - **UserProfile Model**: Migrated to @freezed with immutable data structure and Firebase integration
  - **Account Model**: Migrated to @freezed with proper JSON serialization and metadata handling
  - **Transaction Model**: Migrated to @freezed with proper JSON serialization and tags/metadata handling
  - **Code Generation**: Generated .freezed.dart and .g.dart files using build_runner
  - **Type Safety**: Enhanced type safety with freezed's compile-time checks and immutability guarantees
  - **JSON Serialization**: Robust JSON handling with custom type conversion for complex fields
- **Firestore Security Rules Enhancement**: Comprehensive referential integrity implementation
  - **Referential Integrity Functions**: canDeleteAccount(), canDeleteCategory(), canDeleteTag() safety checks
  - **Reference Validation**: referencedAccountsExist() and referencedCategoryExists() validation functions
  - **Safe Deletion Logic**: Entities must be marked isActive=false before deletion to prevent orphaned data
  - **Transaction Validation**: Enhanced validation to check referenced accounts and categories exist
  - **Budget Validation**: Enhanced validation to check referenced categories exist
  - **Security Rules Testing**: Comprehensive test suite with 32 tests validating all security rule functions
  - **Firebase Test Infrastructure**: Created Firebase test helper and integration test framework
- **Comprehensive Firestore Security Rules**: Complete security implementation with data validation and user isolation
  - **Security Requirements Documentation**: Detailed security requirements and data model analysis
  - **Basic Authentication & Rule Structure**: User data isolation and authentication-based access control
  - **Comprehensive Data Validation**: Field type validation, required fields, and business logic enforcement
    - **User Profile Validation**: Complete validation for user creation/updates with immutable field protection
    - **Account Validation**: Full validation for financial accounts with type/classification enforcement
    - **Transaction Validation**: Business logic validation for income/expense/transfer transactions with account logic
    - **Category & Budget Validation**: Complete validation with proper field constraints and enum enforcement
    - **Goal & Tag Validation**: Additional entity validation for comprehensive coverage
  - **Advanced Security Features**:
    - User data isolation in subcollections under `/users/{userId}/`
    - Authentication required for all operations
    - Owner-only access with strict user ID matching
    - Immutable field protection for critical data
    - Profile deletion prevention via client
  - **Comprehensive Testing Suite**: 31 security rule tests with Firebase Emulator integration
    - **Basic Authentication Tests**: 11 tests covering user data isolation and access control
    - **Data Validation Tests**: 20 tests covering comprehensive field validation and business logic
    - **Test Infrastructure**: Complete Firebase Emulator testing setup with npm test suite
    - **Test Coverage**: Authentication, data validation, edge cases, and security boundaries
  - Comprehensive testing guide (docs/TESTING.md)
- **UI Component Migration**: Complete migration of all remaining UI components to Riverpod architecture
  - MyApp converted to ConsumerWidget with theme mode provider integration
  - SplashScreen converted to ConsumerStatefulWidget with global loading state access
  - All auth widgets (AuthButton, AuthFormField, SocialLoginButton, etc.) migrated to ConsumerWidget/ConsumerStatefulWidget
  - Consistent Riverpod architecture pattern across entire application
  - Dynamic theme switching capability through providers
- **Enhanced Error Handling**: Comprehensive AsyncValue-based error handling system
  - AsyncNotifier providers for all authentication operations (login, signup, Google sign-in, password reset, email verification)
  - AsyncNotifier providers for repository operations (user profile, accounts management)
  - AuthErrorHandler provider for centralized error message management
  - AsyncValue.guard() implementation for robust error propagation across all operations
  - Enhanced UI error display with AsyncValue loading states and error banners
  - Login screen updated with AsyncValue-based providers and enhanced error handling
  - Example implementation in forgot password screen demonstrating AsyncValue pattern

### Fixed
- **CRITICAL: RenderFlex Layout Constraint Issues in Transaction Form Selectors**: Resolved critical layout exceptions preventing transaction creation
  - Fixed unbounded width constraints in AccountSelector dropdown menu items (line 92)
  - Fixed unbounded width constraints in CategorySelector dropdown menu items (lines 102, 133)
  - Fixed unbounded width constraints in ParentCategorySelector dropdown menu items (lines 105, 130)
  - Applied `mainAxisSize: MainAxisSize.min` to 5 Row widgets in DropdownMenuItem components
  - Resolved "RenderFlex children have non-zero flex but incoming width constraints are unbounded" exceptions
  - Transaction creation screen now works without layout constraint errors
  - **TDD Debugging Approach**: Used systematic test-driven debugging to identify and resolve all layout issues
  - **Verification**: All 355 tests passing, flutter analyze clean, layout constraint exceptions eliminated

### Technical Improvements
- **State Management**: Complete migration from StatefulWidget to Riverpod ConsumerStatefulWidget pattern
- **Repository Pattern**: Clean data access layer with dependency injection and comprehensive testing
- **Provider Architecture**: Reactive UI updates with targeted Consumer widgets and loading state management
- **AsyncValue Integration**: Comprehensive error handling with AsyncValue.guard() for all operations
- **Error Management**: Centralized error handling through providers with user-friendly error messages
- **Loading States**: Consistent loading state management through AsyncValue across all UI components
- **Type Safety**: Enhanced type-safe error handling with AsyncValue patterns
- **Test Coverage**: Expanded test coverage for authentication flow and repository operations (135 tests total)
- **Code Quality**: Enhanced error handling, proper mounted checks, and comprehensive documentation
- **CI/CD Ready**: Testing infrastructure prepared for continuous integration
- **Documentation**: Complete testing guide with best practices and troubleshooting

## [0.3.0] - 2024-12-26

### Added
- **Email Verification Flow**: Complete email verification system with user-friendly UI
  - EmailVerificationScreen with Material 3 design and proper theming
  - Automatic email verification sending during signup
  - Periodic verification status checking (every 3 seconds)
  - Manual verification check with "I've Verified My Email" button
  - Resend verification email functionality with 60-second rate limiting
  - Sign out functionality from verification screen
  - Clear instructions and user feedback throughout the process

### Changed
- **AuthWrapper Enhancement**: Updated to check email verification status
  - Routes authenticated users to EmailVerificationScreen if email is unverified
  - Routes authenticated users to HomeScreen if email is verified
  - Maintains existing routing for unauthenticated users to LoginScreen
- **Signup Flow**: Updated to not navigate back to login screen after account creation
  - AuthWrapper now handles routing to EmailVerificationScreen automatically
  - Improved user experience with seamless verification flow
- **Login Flow**: Enhanced to provide feedback about email verification status
  - Shows appropriate messages for verified vs unverified users
  - Maintains existing authentication functionality

### Technical Improvements
- **Error Handling**: Comprehensive Firebase Auth exception handling for email verification
  - Proper error messages for network issues, rate limiting, and other scenarios
  - User-friendly error feedback with appropriate color coding
- **BuildContext Safety**: Proper async operation handling with mounted checks
- **Loading States**: Consistent loading indicators for all verification operations
- **Code Quality**: All tests pass, code analysis clean, proper formatting maintained

## [0.2.0] - 2024-12-25

### Added
- **Email/Password Authentication**: Complete Firebase Auth integration
  - Login screen with Firebase Auth email/password authentication
  - Signup screen with Firebase Auth account creation
  - Comprehensive error handling with user-friendly messages
  - Proper loading states and async operation safety
- **Authentication Validation**: Comprehensive form validation system
  - Email format validation with regex
  - Password strength requirements (minimum 6 characters)
  - Confirm password matching validation
  - Real-time validation feedback

### Changed
- **AuthWrapper**: Enhanced authentication state management
  - Proper Firebase Auth state listening
  - Automatic navigation based on authentication status
  - Improved error handling and loading states

### Technical Improvements
- **Firebase Integration**: Complete Firebase Auth service integration
- **Testing**: Added comprehensive authentication validation tests
- **Code Quality**: Maintained high code quality standards with proper error handling

## [0.1.0] - 2024-12-24

### Added
- **Project Foundation**: Initial Flutter project setup with multi-environment support
  - Development, Staging, and Production environments
  - Firebase integration across all environments
  - Unified main.dart with automatic flavor detection
- **Design System**: Comprehensive Material 3 design system
  - Design tokens for colors, typography, spacing, and borders
  - Automatic dark/light theme support
  - Consistent styling across all components
- **Authentication UI**: Complete authentication user interface
  - Login screen with email/password fields and social login buttons
  - Signup screen with validation and terms acceptance
  - Reusable authentication components (AuthFormField, AuthButton, SocialLoginButton)
  - Form validation with user-friendly error messages
  - Loading states and proper accessibility support
- **Firebase Setup**: Multi-environment Firebase configuration
  - Separate Firebase projects for dev, staging, and prod
  - Automatic Firebase service initialization
  - Service connectivity testing across all environments
- **Development Infrastructure**: Complete development workflow setup
  - Flutter flavors with proper package naming
  - Build scripts and Firebase configuration management
  - Comprehensive testing setup
  - Code quality validation (flutter analyze, dart format)

### Technical Features
- **Architecture**: Clean architecture with proper separation of concerns
- **State Management**: Foundation for Riverpod state management
- **Navigation**: Prepared for go_router integration
- **Performance**: Optimized initialization and cold start performance
- **Security**: Foundation for secure authentication and data handling

[Unreleased]: https://github.com/digitau/budapp/compare/v0.3.0...HEAD
[0.3.0]: https://github.com/digitau/budapp/compare/v0.2.0...v0.3.0
[0.2.0]: https://github.com/digitau/budapp/compare/v0.1.0...v0.2.0
[0.1.0]: https://github.com/digitau/budapp/releases/tag/v0.1.0
