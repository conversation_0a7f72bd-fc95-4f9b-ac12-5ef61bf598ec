# BudApp Documentation

This directory contains comprehensive documentation for the BudApp personal finance management application.

## Quick Start

- [Environment Setup](ENVIRONMENT_SETUP.md) - Development environment configuration
- [Development Workflow](DEVELOPMENT_WORKFLOW.md) - Development processes and guidelines
- [Testing Guide](TESTING.md) - Testing strategies and implementation

## Architecture Documentation

- [Architecture Overview](architecture.md) - Complete system architecture and patterns
- [Generic Form Architecture](GENERIC_FORM_ARCHITECTURE.md) - Configuration-driven form system
- [Shared Form Components](SHARED_FORM_COMPONENTS.md) - Unified form component system

## Feature Documentation

### Authentication & Security
- [Authentication](AUTHENTICATION.md) - Firebase Auth integration and security
- [Session Management](SESSION_MANAGEMENT.md) - User session handling
- [Secure Storage Usage](SECURE_STORAGE_USAGE.md) - Secure data storage patterns

### Data Management
- [Account Management](ACCOUNT_MANAGEMENT_UI_DESIGN.md) - Account features and UI design
- [Category Management](CATEGORY_MANAGEMENT.md) - Category system and hierarchical structure
- [Budget Management](BUDGET_MANAGEMENT.md) - Budget tracking and management
- [Goal Management](GOAL_DATA_SCHEMA.md) - Financial goals tracking and management
- [Transaction Management](TRANSACTION_DATABASE_SCHEMA.md) - Transaction data schema

### UI/UX Design
- [UI/UX Enhancement](UI_UX_ENHANCEMENT.md) - Modern app bar system and interface improvements
- [Bottom Navigation](BOTTOM_NAVIGATION.md) - Navigation structure and patterns
- [Transaction Card UI Design](TRANSACTION_CARD_UI_DESIGN.md) - Transaction display components
- [Time Period Modal Redesign](TIME_PERIOD_MODAL_REDESIGN.md) - Time period selection interface

## Technical Specifications

### Data Schemas
- [Account Data Schema](ACCOUNT_DATA_SCHEMA.md) - Account data structure
- [Transaction Database Schema](TRANSACTION_DATABASE_SCHEMA.md) - Transaction data model
- [Hierarchical Category Data Model](HIERARCHICAL_CATEGORY_DATA_MODEL.md) - Category structure
- [Goal Data Schema](GOAL_DATA_SCHEMA.md) - Financial goals data structure
- [Goal Repository](GOAL_REPOSITORY.md) - Goals data access patterns
- [Firestore Category Collection Structure](FIRESTORE_CATEGORY_COLLECTION_STRUCTURE.md) - Database organization

### Validation & Error Handling
- [Account Validation](ACCOUNT_VALIDATION.md) - Account data validation rules
- [AsyncValue Error Handling](ASYNC_VALUE_ERROR_HANDLING.md) - Error handling patterns

## Development Guidelines

### Code Quality & Standards
- [Code Quality Standards](code_quality_standards.md) - Comprehensive static analysis configuration and quality standards
- [Quick Setup Guide](quick_setup_guide.md) - 5-minute developer setup for code quality tools
- [Flutter App Architecture](rules/flutter_app_architecture.md) - Architecture guidelines
- [Riverpod Patterns](rules/riverpod.md) - State management best practices
- [Effective Dart](rules/effective_dart.md) - Dart coding standards
- [Code Review Guidelines](rules/code_review.md) - Review process and standards

### Testing
- [Testing Strategy](testing/TESTING_STRATEGY.md) - Comprehensive testing approach
- [Testing Implementation Plan](testing/IMPLEMENTATION_PLAN.md) - Testing implementation details
- [Mocktail Usage](rules/mocktail.md) - Testing with mocks

### Firebase Integration
- [Remote Config](REMOTE_CONFIG.md) - Feature flags and configuration
- [Firebase Rules](rules/firebase/) - Security rules and validation

## Troubleshooting

- [Emulator Troubleshooting](EMULATOR_TROUBLESHOOTING.md) - Firebase emulator issues
- [iOS Setup](IOS_SETUP.md) - iOS-specific configuration
- [Flutter Errors](rules/flutter_errors.md) - Common error resolution

## Project History

- [Implementation History](implementation-history.md) - Development timeline and decisions
- [Performance Monitoring](performance-monitoring.md) - Performance tracking and optimization
- [Text Overflow System](text-overflow-system.md) - Global text overflow management

## Refactoring Documentation

- [Refactoring Summaries](refactor/) - Major refactoring efforts and outcomes
- [Navigation Refactoring](NAVIGATION_REFACTORING.md) - Navigation system improvements

## Analysis & Diagrams

- [Current Screen Patterns Analysis](analysis/current_screen_patterns_analysis.md) - UI pattern analysis
- [System Diagrams](diagrams/) - Architecture and flow diagrams

## Task Documentation

- [Task Tracking](tasks/) - Specific task implementation details

---

For the most up-to-date information, always refer to the individual documentation files. Each file contains detailed implementation guidance, code examples, and best practices for its respective area.

## Contributing to Documentation

When adding new features or making changes:

1. Update relevant existing documentation
2. Create new documentation files for significant features
3. Update this README.md index if adding new major sections
4. Follow the established documentation patterns and structure
5. Include code examples and implementation details
6. Update related architecture diagrams if applicable

## Documentation Standards

- Use clear, descriptive headings
- Include code examples for implementation guidance
- Provide both high-level concepts and detailed implementation steps
- Cross-reference related documentation
- Keep documentation current with code changes
- Use Mermaid diagrams for complex flows and relationships
