import 'package:budapp/services/global_error_handler.dart';
import 'package:budapp/services/logging_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockLoggingService extends Mock implements LoggingService {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('GlobalErrorHandler', () {
    late MockLoggingService mockLogger;
    late GlobalErrorHandler errorHandler;

    // Store original handlers to restore
    late FlutterExceptionHandler? originalFlutterErrorHandler;
    late bool Function(Object, StackTrace)? originalPlatformErrorHandler;

    setUpAll(() {
      registerFallbackValue(Exception('test'));
      registerFallbackValue(StackTrace.current);
      registerFallbackValue(const FlutterErrorDetails(exception: 'test'));
    });

    setUp(() {
      mockLogger = MockLoggingService();

      // Create new instance for testing (using factory)
      errorHandler = GlobalErrorHandler();

      // Store original handlers
      originalFlutterErrorHandler = FlutterError.onError;
      originalPlatformErrorHandler = PlatformDispatcher.instance.onError;

      // Configure basic mocks
      when(() => mockLogger.info(any())).thenReturn(null);
      when(() => mockLogger.debug(any())).thenReturn(null);
      when(() => mockLogger.warning(any())).thenReturn(null);
      when(
        () => mockLogger.error(
          any(),
          error: any(named: 'error'),
          stackTrace: any(named: 'stackTrace'),
        ),
      ).thenReturn(null);
    });

    tearDown(() {
      // Restore original handlers
      FlutterError.onError = originalFlutterErrorHandler;
      PlatformDispatcher.instance.onError = originalPlatformErrorHandler;
    });

    group('singleton behavior', () {
      test('returns same instance on multiple calls', () {
        final handler1 = GlobalErrorHandler();
        final handler2 = GlobalErrorHandler();

        expect(handler1, same(handler2));
      });

      test('starts with uninitialized state', () {
        expect(errorHandler.isInitialized, false);
      });
    });

    group('initialization status', () {
      test('reports initialized status correctly', () async {
        expect(errorHandler.isInitialized, false);

        try {
          await errorHandler.initialize(logger: mockLogger);
          // In test environment, Firebase initialization will fail
          // but we can still test the handler logic
        } on Exception {
          // Expected to fail in test environment
        }
      });
    });

    group('error handler setup', () {
      test('sets up Flutter error handler during initialization', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Even if initialization fails, the error handler should be set
        expect(FlutterError.onError, isNotNull);
        // It might be the same as original if initialization failed
      });

      test('handles Flutter errors when handler is set', () async {
        // Set up a simple error handler for testing
        Object? capturedError;
        FlutterError.onError = (FlutterErrorDetails details) {
          capturedError = details.exception;
        };

        final testError = Exception('Test Flutter error');
        final errorDetails = FlutterErrorDetails(
          exception: testError,
          context: ErrorDescription('Test context'),
          stack: StackTrace.current,
        );

        // Trigger the error handler
        FlutterError.onError?.call(errorDetails);

        expect(capturedError, equals(testError));
      });

      test(
        'sets up platform dispatcher error handler during initialization',
        () async {
          try {
            await errorHandler.initialize(logger: mockLogger);
          } on Exception {
            // Expected to fail in test environment due to Firebase
          }

          // Initialization will fail in test environment due to Firebase
          // Just verify no exceptions were thrown
        },
      );
    });

    group('reportError without Firebase', () {
      test('handles reporting when not initialized', () async {
        final testError = Exception('Test error');

        // This should not throw and should handle gracefully
        await expectLater(
          () => errorHandler.reportError(testError, null),
          returnsNormally,
        );

        // When not initialized, logger is not set, so no calls expected
        verifyNoMoreInteractions(mockLogger);
      });

      test('logs error details correctly when not initialized', () async {
        final testError = Exception('Test error with details');
        const testReason = 'Test reason';
        final testCustomKeys = {'key1': 'value1', 'key2': 42};

        await errorHandler.reportError(
          testError,
          null,
          reason: testReason,
          customKeys: testCustomKeys,
          fatal: false,
        );

        // When not initialized, logger is not set, so no calls expected
        verifyNoMoreInteractions(mockLogger);
      });

      test('handles complex error objects', () async {
        final complexError = {
          'type': 'ComplexError',
          'details': {'nested': 'data', 'count': 42},
          'trace': ['step1', 'step2', 'step3'],
        };

        await expectLater(
          () => errorHandler.reportError(complexError, null),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles errors with circular references', () async {
        final circularMap = <String, dynamic>{};
        circularMap['self'] = circularMap; // Create circular reference

        await expectLater(
          () => errorHandler.reportError(circularMap, null),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });
    });

    group('setUserIdentifier without Firebase', () {
      test('does nothing when not initialized', () async {
        await errorHandler.setUserIdentifier('test-user-123');

        // Should not call logger since handler is not initialized
        verifyNoMoreInteractions(mockLogger);
      });

      test('handles null user identifier', () async {
        await expectLater(
          () => errorHandler.setUserIdentifier(null),
          returnsNormally,
        );
      });

      test('handles empty user identifier', () async {
        await expectLater(
          () => errorHandler.setUserIdentifier(''),
          returnsNormally,
        );
      });
    });

    group('logBreadcrumb without Firebase', () {
      test('does nothing when not initialized', () async {
        errorHandler.logBreadcrumb('test message');

        // Should not call logger since handler is not initialized
        verifyNoMoreInteractions(mockLogger);
      });

      test('handles long breadcrumb messages', () async {
        final longMessage = 'x' * 600; // Long message

        expect(() => errorHandler.logBreadcrumb(longMessage), returnsNormally);
      });

      test('handles special characters in breadcrumbs', () async {
        const specialMessage = 'Test with émojis 🎉 and special chars: àáâ';

        expect(
          () => errorHandler.logBreadcrumb(specialMessage),
          returnsNormally,
        );
      });
    });

    group('testCrash', () {
      test('handles test crash request safely', () async {
        // Initialize the error handler first
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // testCrash method handles errors gracefully and doesn't throw to caller
        // It should complete without throwing an exception
        await expectLater(
          errorHandler.testCrash(),
          completes,
        );

        // Note: In test environment, kDebugMode might be false, so warning may not be called
        // The important thing is that the method completes without throwing
      });
    });

    group('concurrency and stress testing', () {
      test('handles multiple concurrent error reports', () async {
        final futures = <Future<void>>[];
        for (var i = 0; i < 50; i++) {
          futures.add(
            errorHandler.reportError(
              Exception('Concurrent error $i'),
              null,
              reason: 'Concurrent test $i',
            ),
          );
        }

        await expectLater(() => Future.wait(futures), returnsNormally);

        // When not initialized, no logger calls expected
        verifyNoMoreInteractions(mockLogger);
      });

      test('handles rapid breadcrumb logging', () async {
        for (var i = 0; i < 100; i++) {
          errorHandler.logBreadcrumb('Rapid breadcrumb $i');
        }

        // Should complete without issues (though nothing logged when not initialized)
        expect(true, isTrue); // Test completion
      });

      test('handles mixed operations concurrently', () async {
        final futures = <Future<void>>[];

        // Mix of different operations
        for (var i = 0; i < 20; i++) {
          futures.add(errorHandler.reportError(Exception('Error $i'), null));
          futures.add(errorHandler.setUserIdentifier('user-$i'));
          errorHandler.logBreadcrumb('Breadcrumb $i');
        }

        await expectLater(() => Future.wait(futures), returnsNormally);
      });
    });

    group('memory management', () {
      test('does not leak memory with many error reports', () async {
        // Create many error objects
        for (var i = 0; i < 1000; i++) {
          final error = Exception('Memory test error $i');
          final stackTrace = StackTrace.current;

          await errorHandler.reportError(
            error,
            stackTrace,
            reason: 'Memory test $i',
            customKeys: {'iteration': i, 'data': 'x' * 100},
          );
        }

        // When not initialized, no logger calls expected
        verifyNoMoreInteractions(mockLogger);
      });

      test('handles large custom data sets', () async {
        final largeData = <String, dynamic>{};
        for (var i = 0; i < 1000; i++) {
          largeData['key$i'] = 'Large value content ' * 50;
        }

        await expectLater(
          () => errorHandler.reportError(
            Exception('Large data test'),
            null,
            customKeys: largeData,
          ),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });
    });

    group('edge cases', () {
      test('handles string errors', () async {
        const stringError = 'Simple string error';

        await expectLater(
          () => errorHandler.reportError(stringError, null),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles numeric errors', () async {
        const numericError = 404;

        await expectLater(
          () => errorHandler.reportError(numericError, null),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles boolean errors', () async {
        const boolError = false;

        await expectLater(
          () => errorHandler.reportError(boolError, null),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles null custom keys gracefully', () async {
        await expectLater(
          () => errorHandler.reportError(
            Exception('Test error'),
            null,
            customKeys: null,
          ),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles empty breadcrumbs list', () async {
        await expectLater(
          () => errorHandler.reportError(
            Exception('Test error'),
            null,
            breadcrumbs: [],
          ),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });

      test('handles null breadcrumbs list', () async {
        await expectLater(
          () => errorHandler.reportError(
            Exception('Test error'),
            null,
            breadcrumbs: null,
          ),
          returnsNormally,
        );

        verifyNoMoreInteractions(mockLogger);
      });
    });

    group('error recovery', () {
      test('continues working after logger errors', () async {
        // When not initialized, logger is not used, so test basic functionality
        await expectLater(
          () => errorHandler.reportError(Exception('First error'), null),
          returnsNormally,
        );

        await expectLater(
          () => errorHandler.reportError(Exception('Second error'), null),
          returnsNormally,
        );

        // No logger interactions when not initialized
        verifyNoMoreInteractions(mockLogger);
      });

      test('maintains state consistency during errors', () async {
        // Error handler should maintain its uninitialized state
        expect(errorHandler.isInitialized, false);

        await errorHandler.reportError(Exception('Test error'), null);

        // State should remain consistent
        expect(errorHandler.isInitialized, false);
      });
    });

    group('Custom Key Management', () {
      test('_setCustomKey handles string values correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Test normal string
        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {'normalKey': 'normalValue'},
        );

        // Test oversized string (should be truncated)
        final longString = 'a' * 1000;
        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {'longKey': longString},
        );

        // Verify logging calls were made
        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('_setCustomKey handles boolean values correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {'boolTrue': true, 'boolFalse': false},
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('_setCustomKey handles numeric values correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {
            'intValue': 42,
            'doubleValue': 3.14,
            'negativeInt': -100,
            'zeroValue': 0,
          },
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('_setCustomKey handles complex objects correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {
            'listValue': [1, 2, 3],
            'mapValue': {'nested': 'value'},
            'nullValue': null,
          },
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('_setCustomKey handles oversized keys correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        final longKey = 'k' * 150; // Longer than 100 character limit
        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {longKey: 'value'},
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('_setCustomKey handles errors gracefully', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // This should not throw even if Firebase operations fail
        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {'testKey': 'testValue'},
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });
    });

    group('User Identifier Management', () {
      test('setUserIdentifier handles valid user ID', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.setUserIdentifier('user123');

        // When not initialized, setUserIdentifier returns early without logging
        // This tests that the method doesn't throw
        expect(errorHandler.isInitialized, false);
      });

      test('setUserIdentifier handles empty user ID', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.setUserIdentifier('');

        // When not initialized, setUserIdentifier returns early without logging
        expect(errorHandler.isInitialized, false);
      });

      test('setUserIdentifier handles null user ID', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.setUserIdentifier(null);

        // When not initialized, setUserIdentifier returns early without logging
        expect(errorHandler.isInitialized, false);
      });

      test('setUserIdentifier handles errors gracefully', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Should not throw even if Firebase operations fail
        await errorHandler.setUserIdentifier('testUser');

        // When not initialized, setUserIdentifier returns early
        expect(errorHandler.isInitialized, false);
      });

      test('setUserIdentifier does nothing when not initialized', () async {
        // Don't initialize the handler
        await errorHandler.setUserIdentifier('user123');

        // Should not call any logger methods
        verifyNever(() => mockLogger.debug(any()));
      });
    });

    group('Breadcrumb Logging', () {
      test('logBreadcrumb handles normal messages', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        errorHandler.logBreadcrumb('User clicked button');

        // When not initialized, logBreadcrumb returns early without logging
        expect(errorHandler.isInitialized, false);
      });

      test('logBreadcrumb truncates oversized messages', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        final longMessage = 'a' * 600; // Longer than 500 character limit
        errorHandler.logBreadcrumb(longMessage);

        // When not initialized, logBreadcrumb returns early
        expect(errorHandler.isInitialized, false);
      });

      test('logBreadcrumb handles errors gracefully', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Should not throw even if Firebase operations fail
        errorHandler.logBreadcrumb('Test breadcrumb');

        // When not initialized, logBreadcrumb returns early
        expect(errorHandler.isInitialized, false);
      });

      test('logBreadcrumb does nothing when not initialized', () async {
        // Don't initialize the handler
        errorHandler.logBreadcrumb('Test breadcrumb');

        // Should not call any logger methods
        verifyNever(() => mockLogger.debug(any()));
        expect(errorHandler.isInitialized, false);
      });

      test('logBreadcrumb handles empty messages', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        errorHandler.logBreadcrumb('');

        // When not initialized, logBreadcrumb returns early
        expect(errorHandler.isInitialized, false);
      });
    });

    group('Test Crash Functionality', () {
      test('testCrash works in debug mode', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // In debug mode, testCrash will try to access Firebase Crashlytics and throw
        await expectLater(
          () => errorHandler.testCrash(),
          throwsA(isA<Exception>()),
        );
      });

      test('testCrash is ignored in release mode', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // In debug mode (test environment), testCrash will throw when accessing Firebase
        await expectLater(
          () => errorHandler.testCrash(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Error Reporting Edge Cases', () {
      test('reportError handles breadcrumbs correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          breadcrumbs: ['Step 1', 'Step 2', 'Step 3'],
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('reportError handles empty breadcrumbs', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          breadcrumbs: [],
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('reportError handles fatal errors correctly', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('fatal error'),
          null,
          fatal: true,
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('reportError handles custom reason', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        await errorHandler.reportError(
          Exception('test'),
          null,
          reason: 'Custom error reason',
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });

      test('reportError handles Firebase reporting failures', () async {
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // This should not throw even if Firebase operations fail
        await errorHandler.reportError(
          Exception('test'),
          null,
          customKeys: {'key': 'value'},
          breadcrumbs: ['breadcrumb'],
        );

        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));
      });
    });

    group('Initialization Edge Cases', () {
      test('multiple initialization calls are safe', () async {
        // First initialization attempt
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Second initialization should not throw
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Both calls should fail due to Firebase not being available
        expect(errorHandler.isInitialized, false);
      });

      test('initialization handles Firebase configuration errors', () async {
        // This test verifies that initialization errors are handled gracefully
        try {
          await errorHandler.initialize(logger: mockLogger);
        } on Exception {
          // Expected to fail in test environment due to Firebase
        }

        // Should log error when initialization fails
        verify(
          () => mockLogger.error(
            any(),
            error: any(named: 'error'),
            stackTrace: any(named: 'stackTrace'),
          ),
        ).called(greaterThan(0));

        expect(errorHandler.isInitialized, false);
      });
    });
  });
}
