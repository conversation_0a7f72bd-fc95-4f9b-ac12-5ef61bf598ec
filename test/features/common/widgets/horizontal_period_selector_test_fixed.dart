import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:budapp/features/common/widgets/horizontal_period_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for comprehensive testing
class MockTimePeriodNotifier extends Mock implements TimePeriodNotifier {}

// Test notifier for controlled state
class TestTimePeriodNotifier extends TimePeriodNotifier {
  TestTimePeriodNotifier(this._initialPeriod);

  final TimePeriod _initialPeriod;

  @override
  TimePeriod build() => _initialPeriod;

  @override
  Future<void> selectPeriod(TimePeriod period) async {
    state = period;
  }
}

// Test callback tracker
class CallbackTracker {
  int callCount = 0;
  void call() => callCount++;
}

void main() {
  setUpAll(() {
    // Register fallback values for Mocktail
    registerFallbackValue(
      TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      ),
    );
  });

  group('HorizontalPeriodSelector', () {
    testWidgets('should render without errors', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
    });

    testWidgets('should display compact period formats', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should find period text in compact format (e.g., "Jan 25", "Feb 25", etc.)
      final currentMonth = TimePeriodService.getCurrentMonth();
      final compactText = TimePeriodService.formatPeriodCompact(currentMonth);

      expect(find.text(compactText), findsOneWidget);
    });

    testWidgets('should handle period selection', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find a period that's not the current one and tap it
      final pageView = find.byType(PageView);
      expect(pageView, findsOneWidget);

      // The widget should be scrollable
      await tester.drag(pageView, const Offset(-100, 0));
      await tester.pumpAndSettle();

      // This basic test ensures the widget renders and is interactive
    });

    testWidgets('should respect minimum height for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(height: 44),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final sizedBox = tester.widget<SizedBox>(
        find
            .descendant(
              of: find.byType(HorizontalPeriodSelector),
              matching: find.byType(SizedBox),
            )
            .first,
      );

      expect(sizedBox.height, equals(44.0));
    });

    testWidgets('should have proper semantics for accessibility', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that the widget has proper semantics
      expect(
        find.bySemanticsLabel('Time period selector'),
        findsOneWidget,
      );
    });

    testWidgets('shows loading state initially', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      // Should show loading indicator initially
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      await tester.pumpAndSettle();

      // After settling, should show the selector
      expect(find.byType(PageView), findsOneWidget);
    });

    testWidgets('handles user interactions with haptic feedback', (
      tester,
    ) async {
      final callbackTracker = CallbackTracker();

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(
                onPeriodChanged: callbackTracker.call,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find gesture detectors for tapping
      final gestureDetectors = find.byType(GestureDetector);
      if (gestureDetectors.evaluate().isNotEmpty) {
        await tester.tap(gestureDetectors.first);
        await tester.pumpAndSettle();

        // Should handle tap interaction
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      }
    });

    testWidgets('shows proper visual states for periods', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should have animated containers for visual states
      expect(find.byType(AnimatedContainer), findsAtLeastNWidgets(1));

      // Should have proper text styling
      expect(find.byType(AnimatedDefaultTextStyle), findsAtLeastNWidgets(1));
    });

    testWidgets('handles allowFutureNavigation parameter correctly', (
      tester,
    ) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(
                allowFutureNavigation: true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final widget = tester.widget<HorizontalPeriodSelector>(
        find.byType(HorizontalPeriodSelector),
      );
      expect(widget.allowFutureNavigation, isTrue);
    });

    testWidgets('synchronizes with external period changes', (tester) async {
      final initialPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      );

      final testNotifier = TestTimePeriodNotifier(initialPeriod);

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            timePeriodNotifierProvider.overrideWith(() => testNotifier),
          ],
          child: const MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Change period externally
      final newPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 3, 1),
        endDate: DateTime(2025, 3, 31),
        displayName: 'March 2025',
        dateRangeText: '01 Mar - 31 Mar',
        year: 2025,
        month: 3,
      );

      await testNotifier.selectPeriod(newPeriod);
      await tester.pumpAndSettle();

      // Widget should still be present and functional
      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
    });

    testWidgets('handles errors gracefully', (tester) async {
      final mockNotifier = MockTimePeriodNotifier();
      when(() => mockNotifier.selectPeriod(any())).thenThrow(
        Exception('Network error'),
      );
      when(mockNotifier.build).thenReturn(
        TimePeriodService.getCurrentMonth(),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            timePeriodNotifierProvider.overrideWith(() => mockNotifier),
          ],
          child: const MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Widget should render despite errors
      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
    });

    testWidgets('performs efficiently with animations', (tester) async {
      await tester.pumpWidget(
        const ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: HorizontalPeriodSelector(),
            ),
          ),
        ),
      );

      // Initial render should be fast
      await tester.pumpAndSettle();
      expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

      // Should show fade animation
      expect(find.byType(FadeTransition), findsOneWidget);

      // Should handle rapid interactions
      final pageView = find.byType(PageView);
      if (pageView.evaluate().isNotEmpty) {
        for (var i = 0; i < 3; i++) {
          await tester.drag(pageView, const Offset(-50, 0));
          await tester.pump(const Duration(milliseconds: 16));
        }

        await tester.pumpAndSettle();
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);
      }
    });

    test('should format periods correctly using service', () {
      final testPeriod = TimePeriod(
        type: PeriodType.monthly,
        startDate: DateTime(2025, 1, 1),
        endDate: DateTime(2025, 1, 31),
        displayName: 'January 2025',
        dateRangeText: '01 Jan - 31 Jan',
        year: 2025,
        month: 1,
      );

      final formatted = TimePeriodService.formatPeriodCompact(testPeriod);
      expect(formatted, equals('Jan 25'));
    });
  });
}
