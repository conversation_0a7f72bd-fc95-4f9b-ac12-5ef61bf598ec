import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/transactions/presentation/screens/transaction_edit_screen.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_metadata_card.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/test_wrapper.dart';

void main() {
  group('TransactionEditScreen', () {
    late List<Transaction> mockTransactions;
    late Transaction testTransaction;

    setUp(() {
      testTransaction = Transaction(
        id: 'test-transaction-id',
        userId: 'test-user',
        type: TransactionType.expense,
        status: TransactionStatus.completed,
        amountCents: 5000, // $50.00
        description: 'Test expense',
        fromAccountId: 'test-account',
        categoryId: 'test-category',
        transactionDate: DateTime(2024, 1, 15),
        notes: 'Test notes',
        createdAt: DateTime(2024, 1, 15),
        updatedAt: DateTime(2024, 1, 15),
      );

      mockTransactions = [testTransaction];
    });

    group('Screen Structure', () {
      testWidgets('renders basic screen structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Verify basic structure
        // Note: TestWrapper creates a MaterialApp which may contain a Scaffold
        expect(find.byType(Scaffold), findsWidgets);
        expect(find.byType(AppBar), findsOneWidget);

        // Verify app bar title
        expect(find.text('Edit Transaction'), findsOneWidget);

        // Verify close button in app bar (there may be multiple close icons due to form FABs)
        expect(
          find.descendant(
            of: find.byType(AppBar),
            matching: find.byIcon(Icons.close),
          ),
          findsOneWidget,
        );
      });

      testWidgets('shows loading state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => const Stream.empty(),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Should show loading indicator initially
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('shows transaction form when data loads', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Should show transaction form
        expect(find.byType(TransactionForm), findsOneWidget);
        expect(find.byType(TransactionMetadataCard), findsOneWidget);
      });
    });

    group('Transaction Not Found', () {
      testWidgets('shows not found state when transaction does not exist', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'non-existent-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Should show not found state
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Transaction Not Found'), findsOneWidget);
        expect(
          find.text(
            "This transaction may have been deleted or you don't have permission to view it",
          ),
          findsOneWidget,
        );
        expect(find.text('Go Back'), findsOneWidget);
      });

      testWidgets('navigates back from not found state', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'non-existent-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Verify go back button exists and is tappable
        expect(find.text('Go Back'), findsOneWidget);

        // Verify the button is actually a button widget
        final goBackButton = find.ancestor(
          of: find.text('Go Back'),
          matching: find.byType(FilledButton),
        );
        expect(goBackButton, findsOneWidget);

        // Should show the not found state
        expect(find.byType(TransactionEditScreen), findsOneWidget);
      });
    });

    group('Error States', () {
      testWidgets('shows error state when data loading fails', (tester) async {
        const testError = 'Failed to load transactions';

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.error(testError),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Should show error state
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Error loading transaction'), findsOneWidget);
        expect(find.text(testError), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
      });

      testWidgets('can retry after error', (tester) async {
        var retryCount = 0;

        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith((ref) {
                if (retryCount == 0) {
                  retryCount++;
                  return Stream.error('Failed to load');
                }
                return Stream.value(mockTransactions);
              }),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Should show error state initially
        expect(find.text('Error loading transaction'), findsOneWidget);

        // Tap retry button
        await tester.tap(find.text('Retry'));
        await tester.pump();

        // Wait for the provider to reload
        await tester.pumpAndSettle();

        // Should show form after retry
        expect(find.byType(TransactionForm), findsOneWidget);
      });
    });

    group('Navigation', () {
      testWidgets('closes screen when close button is tapped', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Verify close button exists in app bar and is tappable
        final appBarCloseButton = find.descendant(
          of: find.byType(AppBar),
          matching: find.byIcon(Icons.close),
        );
        expect(appBarCloseButton, findsOneWidget);

        // Verify the close button is in an IconButton
        final closeButton = find.ancestor(
          of: appBarCloseButton,
          matching: find.byType(IconButton),
        );
        expect(closeButton, findsOneWidget);

        // Should show the screen
        expect(find.byType(TransactionEditScreen), findsOneWidget);
      });
    });

    group('Form Integration', () {
      testWidgets('passes initial transaction to form', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Verify form is present with correct initial data
        final formWidget = tester.widget<TransactionForm>(
          find.byType(TransactionForm),
        );
        expect(formWidget.initialTransaction, equals(testTransaction));
      });
    });

    group('Delete Functionality', () {
      testWidgets('shows delete confirmation dialog', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Get the metadata card and trigger delete
        final metadataCard = tester.widget<TransactionMetadataCard>(
          find.byType(TransactionMetadataCard),
        );

        // Trigger delete callback if it exists
        metadataCard.onDelete?.call();

        await tester.pumpAndSettle();

        // Should show confirmation dialog
        expect(find.byType(AlertDialog), findsOneWidget);
        expect(find.text('Delete Transaction'), findsOneWidget);
        expect(
          find.text(
            'Are you sure you want to delete this transaction? This action cannot be undone.',
          ),
          findsOneWidget,
        );
        expect(find.text('Cancel'), findsOneWidget);
        expect(find.text('Delete'), findsOneWidget);
      });

      testWidgets('cancels delete when cancel is tapped', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Get the metadata card and trigger delete
        final metadataCard = tester.widget<TransactionMetadataCard>(
          find.byType(TransactionMetadataCard),
        );

        metadataCard.onDelete?.call();

        await tester.pumpAndSettle();

        // Tap cancel button
        await tester.tap(find.text('Cancel'));
        await tester.pumpAndSettle();

        // Dialog should be dismissed
        expect(find.byType(AlertDialog), findsNothing);
        expect(find.byType(TransactionEditScreen), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('provides proper semantic information', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidgetWithRouter(
            const TransactionEditScreen(transactionId: 'test-transaction-id'),
            overrides: [
              transactionListProvider.overrideWith(
                (ref) => Stream.value(mockTransactions),
              ),
            ],
            initialLocation: '/test',
          ),
        );

        await tester.pump();

        // Verify accessibility elements
        expect(find.text('Edit Transaction'), findsOneWidget);
        expect(find.byTooltip('Close'), findsOneWidget);
      });
    });
  });
}
