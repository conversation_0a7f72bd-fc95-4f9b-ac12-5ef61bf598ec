import 'package:budapp/config/environment_config.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/auth/services/auth_service.dart';
import 'package:budapp/features/profile/presentation/screens/profile_screen.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/interfaces/firebase_connectivity_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes for ProfileScreen testing
class MockUser extends Mock implements User {}

class MockAuthService extends Mock implements AuthService {}

class MockFirebaseConnectivityService extends Mock
    implements IFirebaseConnectivityService {}

class MockGoRouter extends Mock implements GoRouter {}

void main() {
  group('ProfileScreen', () {
    testWidgets(
      'displays Data Management section with all navigation items',
      (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(const ProfileScreen()),
        );

        await tester.pumpAndSettle();

        // Verify Data Management section header
        expect(find.text('Data Management'), findsOneWidget);

        // Verify Categories navigation item
        expect(find.text('Categories'), findsOneWidget);
        expect(
          find.text('Manage income and expense categories'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.category_outlined), findsOneWidget);

        // Verify Tags navigation item
        expect(find.text('Tags'), findsOneWidget);
        expect(
          find.text('Organize transactions with custom tags'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.label_outline), findsOneWidget);
        expect(
          find.text('Organize transactions with custom tags'),
          findsOneWidget,
        );
        expect(find.byIcon(Icons.label_outline), findsOneWidget);
      },
    );

    testWidgets('displays AppBar with correct title', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const ProfileScreen()),
      );

      await tester.pumpAndSettle();

      // Verify AppBar is present
      expect(find.byType(AppBar), findsOneWidget);

      // Verify profile title is present
      expect(find.text('Profile'), findsOneWidget);
    });

    testWidgets('displays user profile card', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(const ProfileScreen()),
      );

      await tester.pumpAndSettle();

      // Verify user profile card elements
      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget);
    });

    group('User Profile Display Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
      });

      testWidgets('displays verified email badge for verified user', (
        tester,
      ) async {
        // Arrange
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.byIcon(Icons.verified), findsOneWidget);
        expect(find.text('Verified'), findsOneWidget);
      });

      testWidgets('displays no email text when user has no email', (
        tester,
      ) async {
        // Arrange
        when(() => mockUser.email).thenReturn(null);
        when(() => mockUser.emailVerified).thenReturn(false);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.text('No email'), findsOneWidget);
        expect(find.byIcon(Icons.verified), findsNothing);
      });

      testWidgets('does not display verified badge for unverified user', (
        tester,
      ) async {
        // Arrange
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(false);

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Assert
        expect(find.text('<EMAIL>'), findsOneWidget);
        expect(find.byIcon(Icons.verified), findsNothing);
        expect(find.text('Verified'), findsNothing);
      });
    });

    group('Firebase Connectivity Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets(
        'Firebase testing section appears in development environment',
        (tester) async {
          await tester.pumpWidget(
            TestWrapper.createTestWidget(
              const ProfileScreen(),
              overrides: [
                currentUserProvider.overrideWithValue(mockUser),
                authServiceProvider.overrideWithValue(mockAuthService),
                firebaseConnectivityServiceProvider.overrideWithValue(
                  mockConnectivityService,
                ),
              ],
            ),
          );

          await tester.pumpAndSettle();

          // In development, Firebase testing should be visible
          if (EnvironmentConfig.showFirebaseTesting) {
            expect(find.text('Development Info'), findsOneWidget);
            expect(find.text('Test Firebase Services'), findsOneWidget);
          } else {
            expect(find.text('Development Info'), findsNothing);
          }
        },
      );

      testWidgets('Firebase connectivity test success shows status widgets', (
        tester,
      ) async {
        // Arrange - Mock successful Firebase connectivity test
        when(
          () => mockConnectivityService.testFirebaseConnectivity(),
        ).thenAnswer(
          (_) async => {
            'auth': {'status': 'connected'},
            'firestore': {'status': 'connected'},
          },
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Skip test if Firebase testing is not enabled
        if (!EnvironmentConfig.showFirebaseTesting) {
          return;
        }

        // Act - Scroll to make the Firebase test button visible, then tap it
        await tester.scrollUntilVisible(
          find.text('Test Firebase Services'),
          50,
        );
        await tester.tap(find.text('Test Firebase Services'));
        await tester.pumpAndSettle();

        // Assert - Verify status widgets appear
        expect(find.text('Firebase Services Status:'), findsOneWidget);
        expect(
          find.byIcon(Icons.check_circle),
          findsAtLeastNWidgets(2),
        ); // Auth + Firestore
        verify(
          () => mockConnectivityService.testFirebaseConnectivity(),
        ).called(1);
      });

      testWidgets('Firebase connectivity test error shows error status', (
        tester,
      ) async {
        // Arrange - Mock Firebase connectivity test error
        when(
          () => mockConnectivityService.testFirebaseConnectivity(),
        ).thenThrow(Exception('Firebase connection failed'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Skip test if Firebase testing is not enabled
        if (!EnvironmentConfig.showFirebaseTesting) {
          return;
        }

        // Act - Scroll to make the Firebase test button visible, then tap it
        await tester.scrollUntilVisible(
          find.text('Test Firebase Services'),
          50,
        );
        await tester.tap(find.text('Test Firebase Services'));
        await tester.pumpAndSettle();

        // Assert - Error should be handled
        verify(
          () => mockConnectivityService.testFirebaseConnectivity(),
        ).called(1);
      });

      testWidgets('Firebase test button shows loading state', (tester) async {
        // Arrange - Mock slow Firebase connectivity test
        when(
          () => mockConnectivityService.testFirebaseConnectivity(),
        ).thenAnswer((_) async {
          await Future<void>.delayed(const Duration(milliseconds: 100));
          return {
            'auth': {'status': 'connected'},
          };
        });

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Skip test if Firebase testing is not enabled
        if (!EnvironmentConfig.showFirebaseTesting) {
          return;
        }

        // Act - Scroll to make the Firebase test button visible, then tap it
        await tester.scrollUntilVisible(
          find.text('Test Firebase Services'),
          50,
        );
        await tester.tap(find.text('Test Firebase Services'));
        await tester.pump(); // Don't settle - check loading state

        // Assert - Button should show loading indicator
        expect(find.byType(CircularProgressIndicator), findsOneWidget);

        // Wait for the async operation to complete to avoid timer issues
        await tester.pumpAndSettle();
      });
    });

    group('Sign Out Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('sign out success completes without error', (tester) async {
        // Arrange
        when(() => mockAuthService.signOut()).thenAnswer((_) async {});

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Sign Out button visible
        await tester.scrollUntilVisible(find.text('Sign Out'), 50);
        await tester.tap(find.text('Sign Out'));
        await tester.pumpAndSettle();

        // Assert
        verify(() => mockAuthService.signOut()).called(1);
        expect(find.byType(SnackBar), findsNothing); // No error snackbar
      });

      testWidgets('sign out error shows error snackbar', (tester) async {
        // Arrange
        when(
          () => mockAuthService.signOut(),
        ).thenThrow(Exception('Sign out failed'));

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Sign Out button visible
        await tester.scrollUntilVisible(find.text('Sign Out'), 50);
        await tester.tap(find.text('Sign Out'));
        await tester.pumpAndSettle();

        // Assert
        verify(() => mockAuthService.signOut()).called(1);
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.textContaining('Sign out failed'), findsOneWidget);
      });
    });

    group('Coming Soon Features Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('settings tap shows coming soon snackbar', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Settings button visible
        await tester.scrollUntilVisible(find.text('Settings'), 50);
        await tester.tap(find.text('Settings'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Settings - Coming Soon'), findsOneWidget);
      });

      testWidgets('import export tap shows coming soon snackbar', (
        tester,
      ) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Import/Export button visible
        await tester.scrollUntilVisible(find.text('Import/Export'), 50);
        await tester.tap(find.text('Import/Export'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Import/Export - Coming Soon'), findsOneWidget);
      });

      testWidgets('reports tap shows coming soon snackbar', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Reports button visible
        await tester.scrollUntilVisible(find.text('Reports'), 50);
        await tester.tap(find.text('Reports'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Reports - Coming Soon'), findsOneWidget);
      });

      testWidgets('help tap shows coming soon snackbar', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Help button visible
        await tester.scrollUntilVisible(find.text('Help'), 50);
        await tester.tap(find.text('Help'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Help - Coming Soon'), findsOneWidget);
      });

      testWidgets('feedback tap shows coming soon snackbar', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Feedback button visible
        await tester.scrollUntilVisible(find.text('Feedback'), 50);
        await tester.tap(find.text('Feedback'));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(SnackBar), findsOneWidget);
        expect(find.text('Feedback - Coming Soon'), findsOneWidget);
      });
    });

    group('Account Deletion Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('delete account button triggers dialog', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Act - Scroll to make the Delete Account button visible
        await tester.scrollUntilVisible(find.text('Delete Account'), 50);
        await tester.tap(find.text('Delete Account'));
        await tester.pumpAndSettle();

        // Assert - Dialog should appear
        expect(find.byType(AlertDialog), findsOneWidget);
      });
    });

    group('Helper Methods Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('section headers are displayed correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Assert - All section headers should be present
        expect(find.text('Account Management'), findsOneWidget);
        expect(find.text('Data Management'), findsOneWidget);
        expect(find.text('App Settings'), findsOneWidget);
        expect(find.text('Tools & Reports'), findsOneWidget);
        expect(find.text('Help & Support'), findsOneWidget);
        expect(find.text('Account Actions'), findsOneWidget);
      });

      testWidgets('settings items have correct structure', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Verify Categories item structure
        final categoriesListTile = find.ancestor(
          of: find.text('Categories'),
          matching: find.byType(ListTile),
        );
        expect(categoriesListTile, findsOneWidget);

        final categoriesTile = tester.widget<ListTile>(categoriesListTile);
        expect(categoriesTile.leading, isA<Icon>());
        expect(categoriesTile.title, isA<Text>());
        expect(categoriesTile.subtitle, isA<Text>());
        expect(categoriesTile.trailing, isA<Icon>());
        expect(categoriesTile.onTap, isNotNull);
      });
    });

    group('Environment Configuration Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('environment name is displayed correctly', (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const ProfileScreen(),
            overrides: [
              currentUserProvider.overrideWithValue(mockUser),
              authServiceProvider.overrideWithValue(mockAuthService),
              firebaseConnectivityServiceProvider.overrideWithValue(
                mockConnectivityService,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        if (EnvironmentConfig.showFirebaseTesting) {
          expect(
            find.text('Environment: ${EnvironmentConfig.environmentName}'),
            findsOneWidget,
          );
          expect(
            find.textContaining(EnvironmentConfig.firebaseProjectName),
            findsOneWidget,
          );
        }
      });
    });

    group('Integration Tests', () {
      late MockUser mockUser;
      late MockAuthService mockAuthService;
      late MockFirebaseConnectivityService mockConnectivityService;

      setUp(() {
        mockUser = MockUser();
        mockAuthService = MockAuthService();
        mockConnectivityService = MockFirebaseConnectivityService();
        when(() => mockUser.email).thenReturn('<EMAIL>');
        when(() => mockUser.emailVerified).thenReturn(true);
      });

      testWidgets('all navigation buttons are functional', (tester) async {
        // Create a router with all the necessary routes
        final router = GoRouter(
          initialLocation: '/profile',
          routes: [
            GoRoute(
              path: '/profile',
              builder: (context, state) => ProviderScope(
                overrides: [
                  currentUserProvider.overrideWithValue(mockUser),
                  authServiceProvider.overrideWithValue(mockAuthService),
                  firebaseConnectivityServiceProvider.overrideWithValue(
                    mockConnectivityService,
                  ),
                ],
                child: const ProfileScreen(),
              ),
            ),
            GoRoute(
              path: '/categories',
              builder: (context, state) => const Scaffold(
                body: Center(child: Text('Categories Screen')),
              ),
            ),
            GoRoute(
              path: '/budgets',
              builder: (context, state) =>
                  const Scaffold(body: Center(child: Text('Budgets Screen'))),
            ),
            GoRoute(
              path: '/goals',
              builder: (context, state) =>
                  const Scaffold(body: Center(child: Text('Goals Screen'))),
            ),
            GoRoute(
              path: '/tags',
              builder: (context, state) =>
                  const Scaffold(body: Center(child: Text('Tags Screen'))),
            ),
            GoRoute(
              path: '/settings/currency',
              builder: (context, state) => const Scaffold(
                body: Center(child: Text('Currency Settings Screen')),
              ),
            ),
            GoRoute(
              path: '/profile/manage',
              builder: (context, state) => const Scaffold(
                body: Center(child: Text('Profile Management Screen')),
              ),
            ),
          ],
        );

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp.router(
              routerConfig: router,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en'), // English
              ],
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Test that tapping navigation items doesn't crash
        final navigationItems = [
          'Categories',
          'Budgets',
          'Goals',
          'Tags',
          'Currency Settings',
        ];

        for (final item in navigationItems) {
          final finder = find.text(item);
          if (finder.evaluate().isNotEmpty) {
            // Scroll to make the item visible before tapping
            await tester.scrollUntilVisible(finder, 50);
            await tester.tap(finder, warnIfMissed: false);
            await tester.pump(); // Don't settle - just verify no crash
          }
        }
      });

      testWidgets('user profile card navigation works', (tester) async {
        // Create a router with the necessary routes
        final router = GoRouter(
          initialLocation: '/profile',
          routes: [
            GoRoute(
              path: '/profile',
              builder: (context, state) => ProviderScope(
                overrides: [
                  currentUserProvider.overrideWithValue(mockUser),
                  authServiceProvider.overrideWithValue(mockAuthService),
                  firebaseConnectivityServiceProvider.overrideWithValue(
                    mockConnectivityService,
                  ),
                ],
                child: const ProfileScreen(),
              ),
            ),
            GoRoute(
              path: '/profile/manage',
              builder: (context, state) => const Scaffold(
                body: Center(child: Text('Profile Management Screen')),
              ),
            ),
          ],
        );

        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp.router(
              routerConfig: router,
              localizationsDelegates: const [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en'), // English
              ],
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Tap the user profile card
        final profileCard = find.byType(InkWell).first;
        await tester.tap(profileCard);
        await tester.pump(); // Don't settle - just verify no crash
      });
    });
  });
}
