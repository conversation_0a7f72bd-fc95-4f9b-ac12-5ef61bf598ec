import 'package:budapp/data/models/user_profile.dart';
import 'package:budapp/features/profile/providers/profile_providers.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../helpers/mock_providers.dart';

void main() {
  group('Profile Providers Simple Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: MockProviders.authOverrides,
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('should create ProfileUpdate provider', () {
      // Act
      final provider = profileUpdateProvider;

      // Assert
      expect(provider, isNotNull);
      expect(
        provider,
        isA<AutoDisposeAsyncNotifierProvider<ProfileUpdate, void>>(),
      );
    });

    test('should create PasswordChange provider', () {
      // Act
      final provider = passwordChangeProvider;

      // Assert
      expect(provider, isNotNull);
      expect(
        provider,
        isA<AutoDisposeAsyncNotifierProvider<PasswordChange, void>>(),
      );
    });

    test('should create AccountDeletion provider', () {
      // Act
      final provider = accountDeletionProvider;

      // Assert
      expect(provider, isNotNull);
      expect(
        provider,
        isA<AutoDisposeAsyncNotifierProvider<AccountDeletion, void>>(),
      );
    });

    test('should create PasswordReset provider', () {
      // Act
      final provider = passwordResetProvider;

      // Assert
      expect(provider, isNotNull);
      expect(
        provider,
        isA<AutoDisposeAsyncNotifierProvider<PasswordReset, void>>(),
      );
    });

    test('should create userProfile provider', () {
      // Act
      final provider = userProfileProvider;

      // Assert
      expect(provider, isNotNull);
      expect(provider, isA<AutoDisposeFutureProvider<UserProfile?>>());
    });

    test('ProfileUpdate should have initial state', () {
      // Act
      final notifier = container.read(profileUpdateProvider.notifier);
      final state = container.read(profileUpdateProvider);

      // Assert
      expect(notifier, isA<ProfileUpdate>());
      expect(state, isA<AsyncValue<void>>());
    });

    test('PasswordChange should have initial state', () {
      // Act
      final notifier = container.read(passwordChangeProvider.notifier);
      final state = container.read(passwordChangeProvider);

      // Assert
      expect(notifier, isA<PasswordChange>());
      expect(state, isA<AsyncValue<void>>());
    });

    test('AccountDeletion should have initial state', () {
      // Act
      final notifier = container.read(accountDeletionProvider.notifier);
      final state = container.read(accountDeletionProvider);

      // Assert
      expect(notifier, isA<AccountDeletion>());
      expect(state, isA<AsyncValue<void>>());
    });

    test('PasswordReset should have initial state', () {
      // Act
      final notifier = container.read(passwordResetProvider.notifier);
      final state = container.read(passwordResetProvider);

      // Assert
      expect(notifier, isA<PasswordReset>());
      expect(state, isA<AsyncValue<void>>());
    });

    test('should handle provider disposal', () {
      // Act
      final notifier = container.read(profileUpdateProvider.notifier);

      // Assert - Should not throw when disposing
      expect(() => container.dispose(), returnsNormally);
      expect(notifier, isA<ProfileUpdate>());
    });
  });
}
