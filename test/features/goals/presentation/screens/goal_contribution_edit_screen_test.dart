import 'package:budapp/data/models/goal_contribution.dart';
import 'package:budapp/data/repositories/interfaces/i_goal_contribution_repository.dart';
import 'package:budapp/features/goals/presentation/screens/goal_contribution_edit_screen.dart';
import 'package:budapp/features/goals/providers/goal_contribution_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockGoalContributionRepository extends Mock
    implements IGoalContributionRepository {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockUser extends Mock implements User {}

void main() {
  group('GoalContributionEditScreen', () {
    late MockGoalContributionRepository mockRepository;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockFirestoreService mockFirestoreService;
    late MockUser mockUser;
    const testGoalId = 'test-goal-id';
    const testContributionId = 'test-contribution-id';

    late GoalContribution testContribution;

    setUp(() {
      mockRepository = MockGoalContributionRepository();
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      mockFirestoreService = MockFirestoreService();
      mockUser = MockUser();

      // Setup mock behavior
      when(() => mockAuth.currentUser).thenReturn(mockUser);
      when(
        () => mockAuth.authStateChanges(),
      ).thenAnswer((_) => Stream.value(mockUser));
      when(() => mockUser.uid).thenReturn('test-user-id');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);

      // Create test contribution
      testContribution = GoalContribution(
        id: testContributionId,
        goalId: testGoalId,
        userId: 'test-user-id',
        amountCents: 10000, // $100.00
        contributionDate: DateTime(2024, 1, 15),
        description: 'Test contribution',
        createdAt: DateTime(2024, 1, 15),
        updatedAt: DateTime(2024, 1, 15),
      );
    });

    testWidgets('displays loading indicator while loading contribution', (
      tester,
    ) async {
      // Mock repository to return a future that takes time
      when(
        () =>
            mockRepository.getContributionById(testGoalId, testContributionId),
      ).thenAnswer((_) async {
        await Future<void>.delayed(const Duration(milliseconds: 50));
        return testContribution;
      });

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: testGoalId,
            contributionId: testContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      // Pump once to start the async operation
      await tester.pump();

      // Verify loading indicator is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for the future to complete
      await tester.pumpAndSettle();
    });

    testWidgets('displays edit form when contribution is loaded successfully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: testGoalId,
            contributionId: testContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            goalContributionProvider(
              testGoalId,
              testContributionId,
            ).overrideWith((ref) => Future.value(testContribution)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the edit form is displayed
      expect(find.text('Edit Contribution'), findsOneWidget);

      // Verify form fields are present with existing data
      expect(find.textContaining('Contribution Amount'), findsOneWidget);
      expect(find.textContaining('Contribution Date'), findsOneWidget);
      expect(find.textContaining('Description'), findsOneWidget);
    });

    testWidgets('displays not found message when contribution is null', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: testGoalId,
            contributionId: testContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            goalContributionProvider(
              testGoalId,
              testContributionId,
            ).overrideWith((ref) => Future.value(null)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify not found message is displayed
      expect(find.text('Contribution Not Found'), findsOneWidget);
      expect(
        find.text('The requested contribution could not be found.'),
        findsOneWidget,
      );
    });

    testWidgets('displays error message and retry button on error', (
      tester,
    ) async {
      const testError = 'Failed to load contribution data';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: testGoalId,
            contributionId: testContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            goalContributionProvider(
              testGoalId,
              testContributionId,
            ).overrideWith((ref) => Future.error(testError, StackTrace.empty)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify error UI is displayed
      expect(find.text('Error'), findsOneWidget);
      expect(find.text('Failed to load contribution'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('retry button triggers provider invalidation', (tester) async {
      const testError = 'Network error';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: testGoalId,
            contributionId: testContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            goalContributionProvider(
              testGoalId,
              testContributionId,
            ).overrideWith((ref) => Future.error(testError, StackTrace.empty)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Tap the retry button
      await tester.tap(find.text('Retry'));
      await tester.pumpAndSettle();

      // Note: In a real test, we would verify that the provider was invalidated
      // For now, we just verify the button is tappable
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('passes correct goalId and contributionId to providers', (
      tester,
    ) async {
      const customGoalId = 'custom-goal-id';
      const customContributionId = 'custom-contribution-id';

      final customContribution = testContribution.copyWith(
        id: customContributionId,
        goalId: customGoalId,
      );

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalContributionEditScreen(
            goalId: customGoalId,
            contributionId: customContributionId,
          ),
          overrides: [
            goalContributionRepositoryProvider.overrideWithValue(
              mockRepository,
            ),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            goalContributionProvider(
              customGoalId,
              customContributionId,
            ).overrideWith((ref) => Future.value(customContribution)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the screen renders without errors (IDs are passed correctly)
      expect(find.text('Edit Contribution'), findsOneWidget);
    });
  });
}
