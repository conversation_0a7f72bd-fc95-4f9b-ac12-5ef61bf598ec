import 'package:budapp/data/repositories/interfaces/goal_repository.dart';
import 'package:budapp/features/goals/presentation/screens/goal_create_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockGoalRepository extends Mock implements IGoalRepository {}

class MockFirebaseAuth extends Mock implements FirebaseAuth {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockUser extends Mock implements User {}

void main() {
  group('GoalCreateScreen', () {
    late MockGoalRepository mockGoalRepository;
    late MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockFirestoreService mockFirestoreService;
    late MockUser mockUser;

    setUp(() {
      mockGoalRepository = MockGoalRepository();
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirestore();
      mockFirestoreService = MockFirestoreService();
      mockUser = MockUser();

      // Setup mock behavior
      when(() => mockAuth.currentUser).thenReturn(mockUser);
      when(
        () => mockAuth.authStateChanges(),
      ).thenAnswer((_) => Stream.value(mockUser));
      when(() => mockUser.uid).thenReturn('test-user-id');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);
    });

    testWidgets('displays goal creation form with all required fields', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalCreateScreen(),
          overrides: [
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the screen title
      expect(find.text('Create Goal'), findsOneWidget);

      // Verify required form fields are present (with asterisks for required fields)
      expect(find.text('Goal Name *'), findsOneWidget);
      expect(find.text('Target Amount *'), findsOneWidget);
      expect(find.text('Target Date'), findsOneWidget);
      expect(find.text('Status'), findsOneWidget);

      // Verify optional fields are present
      expect(find.text('Description'), findsOneWidget);
      expect(find.text('Goal Color'), findsOneWidget);
      expect(find.text('Goal Icon'), findsOneWidget);

      // Verify Create FAB is present (check icon instead of text)
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets(
      'shows validation errors when Create FAB is tapped with empty required fields',
      (tester) async {
        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            const GoalCreateScreen(),
            overrides: [
              goalRepositoryProvider.overrideWithValue(mockGoalRepository),
              firebaseAuthProvider.overrideWithValue(mockAuth),
              firestoreProvider.overrideWithValue(mockFirestore),
              firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Scroll to make the Create FAB visible, then tap it
        await tester.scrollUntilVisible(
          find.byIcon(Icons.check),
          500, // scroll distance
          scrollable: find.byType(Scrollable).first,
        );
        await tester.tap(find.byIcon(Icons.check));
        await tester.pumpAndSettle();

        // Check if validation errors are shown
        // Note: This test will help us identify if validation is working properly
        // We expect to see validation error messages for required fields
      },
    );

    testWidgets('allows filling out goal creation form', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const GoalCreateScreen(),
          overrides: [
            goalRepositoryProvider.overrideWithValue(mockGoalRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Fill out the goal name - find by hint text instead
      await tester.enterText(
        find.widgetWithText(TextFormField, 'Enter goal name'),
        'Emergency Fund',
      );

      // Fill out the target amount - find by hint text instead
      await tester.enterText(
        find.widgetWithText(TextFormField, '0.00'),
        '10000',
      );

      // Note: We'll need to handle date picker and dropdown interactions
      // in a more comprehensive test

      await tester.pumpAndSettle();

      // Verify the text was entered
      expect(find.text('Emergency Fund'), findsOneWidget);
      expect(find.text('10000'), findsOneWidget);
    });
  });
}
