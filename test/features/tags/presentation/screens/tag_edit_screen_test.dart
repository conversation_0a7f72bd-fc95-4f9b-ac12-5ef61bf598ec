import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/features/tags/presentation/screens/tag_edit_screen.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/services/firestore_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart' as auth_mocks;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../helpers/test_auth_helper.dart';
import '../../../../helpers/test_wrapper.dart';

// Mock classes
class MockTagRepository extends Mock implements TagRepository {}

class MockFirestore extends Mock implements FirebaseFirestore {}

class MockFirestoreService extends Mock implements FirestoreService {}

class MockUser extends Mock implements User {
  @override
  String get uid => 'test-user-id';

  @override
  String? get email => '<EMAIL>';

  @override
  String? get displayName => 'Test User';

  @override
  bool get emailVerified => true;

  @override
  List<UserInfo> get providerData => [];

  @override
  String? get photoURL => null;
}

void main() {
  group('TagEditScreen', () {
    late MockTagRepository mockRepository;
    late auth_mocks.MockFirebaseAuth mockAuth;
    late MockFirestore mockFirestore;
    late MockFirestoreService mockFirestoreService;

    const testTagId = 'test-tag-id';

    late Tag testTag;

    setUp(() {
      mockRepository = MockTagRepository();
      mockAuth = TestAuthHelper.createAuthenticatedMockAuth();
      mockFirestore = MockFirestore();
      mockFirestoreService = MockFirestoreService();

      // Create test tag
      testTag = Tag(
        id: testTagId,
        userId: 'test-user-id',
        name: 'Test Tag',
        color: '#FF5722',
        usageCount: 5,
        createdAt: Timestamp.fromDate(DateTime(2024, 1, 15)),
        updatedAt: Timestamp.fromDate(DateTime(2024, 1, 15)),
      );
    });

    testWidgets('displays loading indicator while loading tag', (tester) async {
      // Mock repository to return a future that takes time
      when(() => mockRepository.getTagById(testTagId)).thenAnswer((_) async {
        await Future<void>.delayed(const Duration(milliseconds: 50));
        return testTag;
      });

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: testTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
          ],
        ),
      );

      // Pump once to start the async operation
      await tester.pump();

      // Verify loading indicator is displayed
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Edit Tag'), findsOneWidget);

      // Wait for the future to complete
      await tester.pumpAndSettle();
    });

    testWidgets('displays edit form when tag is loaded successfully', (
      tester,
    ) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: testTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            tagByIdProvider(
              testTagId,
            ).overrideWith((ref) => Future.value(testTag)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the edit form is displayed
      expect(find.text('Edit Tag'), findsOneWidget);

      // Verify form fields are present
      expect(find.textContaining('Name'), findsOneWidget);
      expect(find.textContaining('Color'), findsOneWidget);

      // Verify Save FAB is present (FloatingActionButton with checkmark icon)
      expect(
        find.descendant(
          of: find.byType(FloatingActionButton),
          matching: find.byIcon(Icons.check),
        ),
        findsOneWidget,
      );
    });

    testWidgets('displays not found message when tag is null', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: testTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            tagByIdProvider(
              testTagId,
            ).overrideWith((ref) => Future.value(null)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify not found message is displayed
      expect(find.text('Tag Not Found'), findsOneWidget);
      expect(
        find.text('The requested tag could not be found.'),
        findsOneWidget,
      );
    });

    testWidgets('displays error message on error', (tester) async {
      const testError = 'Failed to load tag data';

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: testTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            tagByIdProvider(
              testTagId,
            ).overrideWith((ref) => Future.error(testError)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify error UI is displayed
      expect(find.text('Error'), findsOneWidget);
      expect(find.text('Error loading tag: $testError'), findsOneWidget);
    });

    testWidgets('passes correct tagId to providers', (tester) async {
      const customTagId = 'custom-tag-id';

      final customTag = testTag.copyWith(id: customTagId);

      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: customTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            tagByIdProvider(
              customTagId,
            ).overrideWith((ref) => Future.value(customTag)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the screen renders without errors (ID is passed correctly)
      expect(find.text('Edit Tag'), findsOneWidget);
    });

    testWidgets('handles field changes with debug print', (tester) async {
      await tester.pumpWidget(
        TestWrapper.createTestWidget(
          const TagEditScreen(tagId: testTagId),
          overrides: [
            tagRepositoryProvider.overrideWithValue(mockRepository),
            firebaseAuthProvider.overrideWithValue(mockAuth),
            firestoreProvider.overrideWithValue(mockFirestore),
            firestoreServiceProvider.overrideWithValue(mockFirestoreService),
            tagByIdProvider(
              testTagId,
            ).overrideWith((ref) => Future.value(testTag)),
          ],
        ),
      );

      await tester.pumpAndSettle();

      // Verify the form is displayed and can handle field changes
      expect(find.text('Edit Tag'), findsOneWidget);

      // Find and interact with the name field
      final nameField = find.widgetWithText(TextFormField, 'Test Tag');
      expect(nameField, findsOneWidget);

      // Clear and enter new text
      await tester.enterText(nameField, 'Updated Tag Name');
      await tester.pumpAndSettle();

      // Verify the text was entered
      expect(find.text('Updated Tag Name'), findsOneWidget);
    });
  });
}
