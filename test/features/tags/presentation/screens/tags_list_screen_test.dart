import 'dart:async';

import 'package:budapp/data/models/tag.dart';
import 'package:budapp/data/repositories/interfaces/tag_repository.dart';
import 'package:budapp/features/tags/presentation/screens/tags_list_screen.dart';
import 'package:budapp/features/tags/presentation/widgets/tag_card.dart';
import 'package:budapp/features/tags/providers/tag_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

class MockTagRepository extends Mock implements TagRepository {}

class MockGoRouter extends Mock implements GoRouter {}

class Listener<T> extends Mock {
  void call(T? previous, T value);
}

// Create a fake AsyncValue for testing
class AsyncValueFake<T> extends Fake {
  // This is a mock implementation for testing purposes only
}

void main() {
  setUpAll(() {
    // Register fallback values for AsyncValue
    registerFallbackValue(AsyncValueFake<List<Tag>>());
  });

  group('TagsListScreen Provider Tests', () {
    late MockTagRepository mockTagRepository;
    late ProviderContainer container;

    setUp(() {
      mockTagRepository = MockTagRepository();

      // Setup mock repository methods
      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.value([]));

      when(
        () => mockTagRepository.searchTags(any()),
      ).thenAnswer((_) => Future.value([]));

      when(
        () => mockTagRepository.getTagUsageCount(any()),
      ).thenAnswer((_) => Future.value(5));

      container = ProviderContainer(
        overrides: [tagRepositoryProvider.overrideWithValue(mockTagRepository)],
      );
    });

    test('userTagsProvider should return tags from repository', () async {
      // Setup mock to return tags
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'Groceries',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
        Tag(
          id: 'tag-2',
          userId: 'test-user-123',
          name: 'Entertainment',
          color: '#33FF57',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.value(testTags));

      // Listen to the provider
      final listener = Listener<AsyncValue<List<Tag>>>();
      container.listen<AsyncValue<List<Tag>>>(
        userTagsProvider,
        listener.call,
        fireImmediately: true,
      );

      // Wait for the stream to emit
      await Future<void>.delayed(Duration.zero);

      // Verify the provider returns the correct data
      final result = await container.read(userTagsProvider.future);
      expect(result.length, 2);
      expect(result[0].name, 'Groceries');
      expect(result[1].name, 'Entertainment');
    });

    test('tagSearchProvider should search tags from repository', () async {
      // Setup mock to return search results
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'Groceries',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      when(
        () => mockTagRepository.searchTags('Groceries'),
      ).thenAnswer((_) => Future.value(testTags));

      // Get the notifier
      final notifier = container.read(tagSearchProvider.notifier);

      // Perform search
      await notifier.searchTags('Groceries');

      // Verify the search was called
      verify(() => mockTagRepository.searchTags('Groceries')).called(1);

      // Verify the provider returns the correct data
      final result = container.read(tagSearchProvider).value;
      expect(result?.length, 1);
      expect(result?[0].name, 'Groceries');
    });

    test('tagSearchProvider should clear search', () async {
      // Setup mock to return empty list on clear
      when(
        () => mockTagRepository.searchTags(''),
      ).thenAnswer((_) => Future.value([]));

      // Reset the mock to clear previous calls
      reset(mockTagRepository);

      // Setup the mock again after reset
      when(
        () => mockTagRepository.searchTags(''),
      ).thenAnswer((_) => Future.value([]));

      // Get the notifier
      final notifier = container.read(tagSearchProvider.notifier);

      // Clear search
      await notifier.clearSearch();

      // Verify the search was called with empty string
      // The clearSearch method might call searchTags twice internally
      verify(() => mockTagRepository.searchTags('')).called(2);
    });
  });

  group('TagsListScreen Widget Tests', () {
    late MockTagRepository mockTagRepository;

    setUp(() {
      mockTagRepository = MockTagRepository();

      // Setup default mock behaviors
      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.value([]));
      when(
        () => mockTagRepository.searchTags(any()),
      ).thenAnswer((_) => Future.value([]));
      when(
        () => mockTagRepository.getTagUsageCount(any()),
      ).thenAnswer((_) => Future.value(5));
    });

    Widget createTestWidget({List<Override>? overrides}) {
      return ProviderScope(
        overrides: [
          tagRepositoryProvider.overrideWithValue(mockTagRepository),
          ...?overrides,
        ],
        child: const MaterialApp(home: TagsListScreen()),
      );
    }

    Widget createTestWidgetWithTags(List<Tag> tags) {
      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.value(tags));

      return createTestWidget();
    }

    testWidgets('should display app bar with correct title', (tester) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      expect(find.text('Tags'), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should display basic UI components', (tester) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Check for basic UI components (expect 2 Scaffolds due to GlobalFabSystem wrapper)
      expect(find.byType(Scaffold), findsNWidgets(2));
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });

    testWidgets('should display tags list when tags exist', (tester) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag1',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
        Tag(
          id: 'tag-2',
          userId: 'test-user-123',
          name: 'TestTag2',
          color: '#33FF57',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.value(testTags));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byType(TagCard), findsNWidgets(2));
    });

    testWidgets('should display loading indicator while loading tags', (
      tester,
    ) async {
      final controller = StreamController<List<Tag>>();
      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => controller.stream);

      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      await controller.close();
    });

    testWidgets('should display error state when loading tags fails', (
      tester,
    ) async {
      when(
        () => mockTagRepository.watchUserTags(),
      ).thenAnswer((_) => Stream.error('Failed to load tags'));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      // The error message appears twice - once in the title and once in the body
      expect(find.text('Failed to load tags'), findsAtLeastNWidgets(1));
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should enter search mode when search button is pressed', (
      tester,
    ) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Tap search button
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      expect(find.text('Search Tags'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should perform search when text is entered in search field', (
      tester,
    ) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Enter search mode
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Enter search text
      await tester.enterText(find.byType(TextField), 'TestSearch');
      await tester.pump();

      verify(() => mockTagRepository.searchTags('TestSearch')).called(1);
    });

    testWidgets('should display no results message when search returns empty', (
      tester,
    ) async {
      when(
        () => mockTagRepository.searchTags('NonExistent'),
      ).thenAnswer((_) => Future.value([]));

      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Enter search mode
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Enter search text
      await tester.enterText(find.byType(TextField), 'NonExistent');
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.search_off), findsOneWidget);
      expect(find.text('No tags found'), findsOneWidget);
      expect(find.text('Try a different search term'), findsOneWidget);
    });

    testWidgets('should support pull to refresh', (tester) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Verify refresh indicator exists
      expect(find.byType(RefreshIndicator), findsOneWidget);
    });

    testWidgets('should dispose search controller properly', (tester) async {
      final now = Timestamp.now();
      final testTags = [
        Tag(
          id: 'tag-1',
          userId: 'test-user-123',
          name: 'TestTag',
          color: '#FF5733',
          createdAt: now,
          updatedAt: now,
        ),
      ];

      await tester.pumpWidget(createTestWidgetWithTags(testTags));
      await tester.pumpAndSettle();

      // Remove the widget to trigger dispose
      await tester.pumpWidget(Container());
      await tester.pumpAndSettle();

      // No exceptions should be thrown during disposal
    });
  });
}
