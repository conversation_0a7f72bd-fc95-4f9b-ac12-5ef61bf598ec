import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/screens/account_edit_screen.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../helpers/mock_data_factory.dart';
import '../../helpers/mock_providers.dart' as mp;
import '../../helpers/test_wrapper.dart';

void main() {
  group('AccountEditScreen Tests', () {
    late mp.MockAccountRepository mockAccountRepository;
    late mp.MockAuthService mockAuthService;
    late MockUser mockUser;

    setUpAll(() {
      registerFallbackValue(
        Account(
          id: 'test-id',
          userId: 'test-user',
          name: 'Test Account',
          type: AccountType.checking,
          classification: AccountClassification.asset,
          initialBalanceCents: 0,
          currentBalanceCents: 0,
          isPrimary: false,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {},
        ),
      );
    });

    setUp(() {
      mockAccountRepository = mp.MockAccountRepository();
      mockAuthService = mp.MockAuthService();
      mockUser = MockUser();

      // Setup default mock behaviors for authentication
      when(() => mockUser.uid).thenReturn('test-user-123');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);
      when(() => mockAuthService.currentUser).thenReturn(mockUser);

      // Setup mock repository methods
      when(
        () => mockAccountRepository.updateAccount(any(), any()),
      ).thenAnswer((_) async {});
    });

    Widget buildTestWidget(String accountId, {List<Override>? overrides}) {
      return TestWrapper.createCategoryTestWidgetWithRouter(
        AccountEditScreen(accountId: accountId),
        initialLocation: '/accounts/edit/$accountId',
        overrides: [
          accountRepositoryProvider.overrideWithValue(mockAccountRepository),
          authServiceProvider.overrideWithValue(mockAuthService),
          ...?overrides,
        ],
      );
    }

    testWidgets('should display loading screen while fetching account', (
      tester,
    ) async {
      const accountId = 'test-account-id';

      // Setup mock to return empty stream (loading state)
      when(
        () => mockAccountRepository.watchAccountForUser(any(), accountId),
      ).thenAnswer((_) => const Stream.empty());

      await tester.pumpWidget(buildTestWidget(accountId));

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Edit Account'), findsOneWidget);
    });

    testWidgets('should display error screen for invalid account ID', (
      tester,
    ) async {
      const accountId = 'invalid-account-id';

      // Setup mock to return error stream
      when(
        () => mockAccountRepository.watchAccountForUser(any(), accountId),
      ).thenAnswer((_) => Stream.error(Exception('Account not found')));

      await tester.pumpWidget(buildTestWidget(accountId));
      await tester.pump();

      // Should show error screen
      expect(find.text('Error'), findsOneWidget);
      expect(find.textContaining('Error loading account:'), findsOneWidget);
    });

    testWidgets('should display account not found screen for null account', (
      tester,
    ) async {
      const accountId = 'null-account-id';

      // Setup mock to return null account
      when(
        () => mockAccountRepository.watchAccountForUser(any(), accountId),
      ).thenAnswer((_) => Stream.value(null));

      await tester.pumpWidget(buildTestWidget(accountId));
      await tester.pump();

      // Should show account not found screen
      expect(find.text('Account Not Found'), findsOneWidget);
      expect(
        find.text('The requested account could not be found.'),
        findsOneWidget,
      );
    });

    testWidgets('should display edit form with pre-populated data', (
      tester,
    ) async {
      const accountId = 'test-account-id';
      final mockAccount = MockDataFactory.createAccount(
        id: accountId,
        name: 'Test Checking Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 150000, // $1500.00
        description: 'My test account',
        colorHex: '#FF5722',
        iconName: 'account_balance',
      );

      // Setup mock to return the test account
      when(
        () => mockAccountRepository.watchAccountForUser(any(), accountId),
      ).thenAnswer((_) => Stream.value(mockAccount));

      await tester.pumpWidget(
        buildTestWidget(
          accountId,
          overrides: [
            // Override the update provider to prevent actual updates
            accountUpdateProvider.overrideWith(MockAccountUpdateNotifier.new),
          ],
        ),
      );
      await tester.pump();

      // Should show the edit form
      expect(find.text('Edit Account'), findsOneWidget);

      // Check that the form is rendered
      expect(find.byType(Form), findsOneWidget);

      // Wait for form to fully render
      await tester.pumpAndSettle();

      // Check for form fields - should have text fields for name, description, initialBalance
      expect(find.byType(TextFormField), findsAtLeastNWidgets(1));

      // Check for save FAB (FloatingActionButton with check icon)
      expect(
        find.descendant(
          of: find.byType(FloatingActionButton),
          matching: find.byIcon(Icons.check),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should handle form submission with valid data', (
      tester,
    ) async {
      const accountId = 'test-account-id';
      final mockAccount = MockDataFactory.createAccount(
        id: accountId,
        name: 'Original Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 100000,
      );

      // Setup mock to return the test account
      when(
        () => mockAccountRepository.watchAccountForUser(any(), accountId),
      ).thenAnswer((_) => Stream.value(mockAccount));

      await tester.pumpWidget(buildTestWidget(accountId));

      // Wait for the form to be fully loaded
      await tester.pumpAndSettle();

      // Verify the form is displayed with the account data
      expect(find.text('Edit Account'), findsOneWidget);

      // Check that the form is rendered and fields are available
      expect(find.byType(Form), findsOneWidget);

      // Verify that form fields are present (should have text fields for name, description, initialBalance)
      expect(find.byType(TextFormField), findsAtLeastNWidgets(1));

      // Verify save FAB is available (FloatingActionButton with check icon)
      expect(
        find.descendant(
          of: find.byType(FloatingActionButton),
          matching: find.byIcon(Icons.check),
        ),
        findsOneWidget,
      );

      // Test that we can interact with form fields without issues
      // Find text field for account name and verify it exists
      final nameFields = find.byType(TextFormField);
      if (tester.any(nameFields)) {
        // Enter text in the first text field (likely the name field)
        await tester.enterText(nameFields.first, 'Updated Account Name');
        await tester.pump();
      }

      // Scroll to make the save FAB visible and tap it
      final saveFab = find.descendant(
        of: find.byType(FloatingActionButton),
        matching: find.byIcon(Icons.check),
      );
      await tester.ensureVisible(saveFab);
      await tester.pump();

      // Tap the save FAB to test form submission
      await tester.tap(saveFab);
      await tester.pumpAndSettle();

      // The test passes if we can interact with the form without crashes
      // Note: Full form submission testing would require complex setup of all form fields
    });
  });
}

/// Mock implementation of AccountUpdateNotifier for testing
class MockAccountUpdateNotifier extends AccountUpdateNotifier {
  void Function(Account)? updateAccountCallback;
  AsyncValue<void> _state = const AsyncValue.data(null);

  @override
  AsyncValue<void> get state => _state;

  @override
  set state(AsyncValue<void> newState) {
    _state = newState;
  }

  @override
  Future<void> updateAccount(Account account) async {
    _state = const AsyncValue.loading();

    // Simulate the update operation
    updateAccountCallback?.call(account);

    // Simulate successful completion
    _state = const AsyncValue.data(null);
  }

  @override
  Future<void> deleteAccount(String accountId) async {
    _state = const AsyncValue.loading();
    // Simulate successful completion
    _state = const AsyncValue.data(null);
  }

  @override
  Future<void> deactivateAccount(String accountId) async {
    _state = const AsyncValue.loading();
    // Simulate successful completion
    _state = const AsyncValue.data(null);
  }

  @override
  Future<void> setPrimaryAccount(String accountId) async {
    _state = const AsyncValue.loading();
    // Simulate successful completion
    _state = const AsyncValue.data(null);
  }
}
