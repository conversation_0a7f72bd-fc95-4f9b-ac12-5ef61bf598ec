import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_card.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../helpers/mock_data_factory.dart';

void main() {
  group('AccountCard', () {
    late Account testAccount;

    setUp(() {
      testAccount = MockDataFactory.createAccount(
        name: 'Test Checking Account',
        type: AccountType.checking,
        classification: AccountClassification.asset,
        initialBalanceCents: 150000, // $1500.00
        description: 'Test account description',
        isPrimary: false,
        isActive: true,
      );
    });

    Widget createTestWidget({
      required Account account,
      VoidCallback? onTap,
      VoidCallback? onEdit,
      VoidCallback? onDelete,
      VoidCallback? onSetPrimary,
      VoidCallback? onDeactivate,
      bool showActions = false,
    }) {
      return ProviderScope(
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en')],
          home: Scaffold(
            body: AccountCard(
              account: account,
              onTap: onTap,
              onEdit: onEdit,
              onDelete: onDelete,
              onSetPrimary: onSetPrimary,
              onDeactivate: onDeactivate,
              showActions: showActions,
            ),
          ),
        ),
      );
    }

    testWidgets('should render account name and balance', (tester) async {
      await tester.pumpWidget(createTestWidget(account: testAccount));

      expect(find.text('Test Checking Account'), findsOneWidget);
      expect(find.text('1500.00'), findsOneWidget);
    });

    testWidgets('should display correct icon for account type', (tester) async {
      await tester.pumpWidget(createTestWidget(account: testAccount));

      // Should display checking account icon
      expect(find.byIcon(Icons.account_balance), findsOneWidget);
    });

    testWidgets('should display primary badge when account is primary', (
      tester,
    ) async {
      final primaryAccount = testAccount.copyWith(isPrimary: true);
      await tester.pumpWidget(createTestWidget(account: primaryAccount));

      expect(find.text('PRIMARY'), findsOneWidget);
    });

    testWidgets('should display inactive badge when account is inactive', (
      tester,
    ) async {
      final inactiveAccount = testAccount.copyWith(isActive: false);
      await tester.pumpWidget(createTestWidget(account: inactiveAccount));

      expect(find.text('Inactive'), findsOneWidget);
    });

    testWidgets('should display description when available', (tester) async {
      await tester.pumpWidget(createTestWidget(account: testAccount));

      expect(find.text('Test account description'), findsOneWidget);
    });

    testWidgets('should not display description when empty', (tester) async {
      final accountWithoutDescription = testAccount.copyWith(description: '');
      await tester.pumpWidget(
        createTestWidget(account: accountWithoutDescription),
      );

      expect(find.text('Test account description'), findsNothing);
    });

    testWidgets('should call onTap when card is tapped', (tester) async {
      var tapped = false;
      await tester.pumpWidget(
        createTestWidget(account: testAccount, onTap: () => tapped = true),
      );

      await tester.tap(find.byType(AccountCard));
      expect(tapped, isTrue);
    });

    testWidgets('should display popup menu with all actions', (tester) async {
      await tester.pumpWidget(
        createTestWidget(account: testAccount, showActions: true),
      );

      // Tap the menu button
      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Check all menu items are present
      expect(find.text('Edit Account'), findsOneWidget);
      expect(find.text('Set as Primary'), findsOneWidget);
      expect(find.text('Deactivate Account'), findsOneWidget);
      expect(find.text('Delete Account'), findsOneWidget);
    });

    testWidgets('should call onEdit when edit menu item is selected', (
      tester,
    ) async {
      var editCalled = false;
      await tester.pumpWidget(
        createTestWidget(
          account: testAccount,
          onEdit: () => editCalled = true,
          showActions: true,
        ),
      );

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Edit Account'));

      expect(editCalled, isTrue);
    });

    testWidgets(
      'should call onSetPrimary when set primary menu item is selected',
      (tester) async {
        var setPrimaryCalled = false;
        await tester.pumpWidget(
          createTestWidget(
            account: testAccount,
            onSetPrimary: () => setPrimaryCalled = true,
            showActions: true,
          ),
        );

        await tester.tap(find.byIcon(Icons.more_vert));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Set as Primary'));

        expect(setPrimaryCalled, isTrue);
      },
    );

    testWidgets(
      'should call onDeactivate when deactivate menu item is selected',
      (tester) async {
        var deactivateCalled = false;
        await tester.pumpWidget(
          createTestWidget(
            account: testAccount,
            onDeactivate: () => deactivateCalled = true,
            showActions: true,
          ),
        );

        await tester.tap(find.byIcon(Icons.more_vert));
        await tester.pumpAndSettle();
        await tester.tap(find.text('Deactivate Account'));

        expect(deactivateCalled, isTrue);
      },
    );

    testWidgets('should call onDelete when delete menu item is selected', (
      tester,
    ) async {
      var deleteCalled = false;
      await tester.pumpWidget(
        createTestWidget(
          account: testAccount,
          onDelete: () => deleteCalled = true,
          showActions: true,
        ),
      );

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Delete Account'));

      expect(deleteCalled, isTrue);
    });

    group('Account Type Icons', () {
      testWidgets('should display savings icon for savings account', (
        tester,
      ) async {
        final savingsAccount = testAccount.copyWith(type: AccountType.savings);
        await tester.pumpWidget(createTestWidget(account: savingsAccount));

        expect(find.byIcon(Icons.savings), findsOneWidget);
      });

      testWidgets('should display credit card icon for credit card account', (
        tester,
      ) async {
        final creditCardAccount = testAccount.copyWith(
          type: AccountType.creditCard,
        );
        await tester.pumpWidget(createTestWidget(account: creditCardAccount));

        expect(find.byIcon(Icons.credit_card), findsOneWidget);
      });

      testWidgets('should display cash icon for cash account', (tester) async {
        final cashAccount = testAccount.copyWith(type: AccountType.cash);
        await tester.pumpWidget(createTestWidget(account: cashAccount));

        expect(find.byIcon(Icons.account_balance_wallet), findsOneWidget);
      });

      testWidgets('should display investment icon for investment account', (
        tester,
      ) async {
        final investmentAccount = testAccount.copyWith(
          type: AccountType.investment,
        );
        await tester.pumpWidget(createTestWidget(account: investmentAccount));

        expect(find.byIcon(Icons.trending_up), findsOneWidget);
      });

      testWidgets('should display loan icon for loan account', (tester) async {
        final loanAccount = testAccount.copyWith(type: AccountType.loan);
        await tester.pumpWidget(createTestWidget(account: loanAccount));

        expect(find.byIcon(Icons.account_balance_outlined), findsOneWidget);
      });
    });

    group('Balance Color Logic', () {
      testWidgets('should display positive asset balance in normal color', (
        tester,
      ) async {
        final assetAccount = testAccount.copyWith(
          classification: AccountClassification.asset,
          currentBalanceCents: 100000,
        );
        await tester.pumpWidget(createTestWidget(account: assetAccount));

        // Find the balance text widget
        expect(find.text('1000.00'), findsOneWidget);
      });

      testWidgets('should display negative asset balance in error color', (
        tester,
      ) async {
        final assetAccount = testAccount.copyWith(
          classification: AccountClassification.asset,
          currentBalanceCents: -50000,
        );
        await tester.pumpWidget(createTestWidget(account: assetAccount));

        // Find the balance text widget
        expect(find.text('-500.00'), findsOneWidget);
      });

      testWidgets('should display positive liability balance in error color', (
        tester,
      ) async {
        final liabilityAccount = testAccount.copyWith(
          classification: AccountClassification.liability,
          currentBalanceCents: 50000,
        );
        await tester.pumpWidget(createTestWidget(account: liabilityAccount));

        // Find the balance text widget
        expect(find.text('500.00'), findsOneWidget);
      });
    });

    testWidgets('should use custom icon when iconName is provided', (
      tester,
    ) async {
      final accountWithCustomIcon = testAccount.copyWith(iconName: 'home');
      await tester.pumpWidget(createTestWidget(account: accountWithCustomIcon));

      // Should use custom icon instead of type-based icon
      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.account_balance), findsNothing);
    });

    testWidgets('should handle invalid color hex gracefully', (tester) async {
      final accountWithInvalidColor = testAccount.copyWith(
        colorHex: 'invalid-color',
      );
      await tester.pumpWidget(
        createTestWidget(account: accountWithInvalidColor),
      );

      // Should not crash and should render the widget
      expect(find.byType(AccountCard), findsOneWidget);
    });
  });
}
