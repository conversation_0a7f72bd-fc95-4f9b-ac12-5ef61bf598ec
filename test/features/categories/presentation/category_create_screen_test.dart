import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/categories/presentation/screens/category_create_screen.dart';
import 'package:budapp/providers/providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/mock_providers.dart' as mp;
import '../../../helpers/test_wrapper.dart';

void main() {
  group('CategoryCreateScreen Tests', () {
    late mp.MockCategoryRepository mockCategoryRepository;
    late MockUser mockUser;

    setUpAll(() {
      registerFallbackValue(
        Category(
          id: 'test-id',
          userId: 'test-user',
          name: 'Test Category',
          type: CategoryType.expense,
          parentId: null,
          description: 'Test description',
          color: '#FF0000',
          icon: 'category',
          isActive: true,
          schemaVersion: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {},
        ),
      );
    });

    setUp(() {
      mockCategoryRepository = mp.MockCategoryRepository();
      mockUser = MockUser();

      // Setup default mock behaviors
      when(() => mockUser.uid).thenReturn('test-user-123');
      when(() => mockUser.email).thenReturn('<EMAIL>');
      when(() => mockUser.emailVerified).thenReturn(true);
    });

    Widget buildTestWidget({List<Override>? overrides}) {
      return TestWrapper.createCategoryTestWidgetWithRouter(
        const CategoryCreateScreen(),
        overrides: [
          categoryRepositoryProvider.overrideWithValue(mockCategoryRepository),
          currentUserProvider.overrideWithValue(mockUser),
          ...?overrides,
        ],
      );
    }

    testWidgets('should display category creation form', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Verify form elements are present
      expect(find.text('Create Category'), findsOneWidget);

      // With Material 3 floating labels, check for form fields instead of static text
      expect(
        find.byType(TextFormField),
        findsAtLeastNWidgets(2),
      ); // Name and Description fields

      // Check for dropdown form field for category type
      expect(
        find.byType(DropdownButtonFormField<CategoryType>),
        findsOneWidget,
      );
    });

    testWidgets('should display parent category selector for subcategories', (
      tester,
    ) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Should show parent category field
      expect(find.text('Parent Category'), findsOneWidget);
    });

    testWidgets('should validate name length limit', (tester) async {
      await tester.pumpWidget(buildTestWidget());
      await tester.pumpAndSettle();

      // Enter name that's too long
      final nameField = find.byType(TextFormField).first;
      await tester.enterText(nameField, 'A' * 101); // 101 characters
      await tester.pumpAndSettle();

      // Look for FloatingActionButton (submit is handled by form FAB)
      final submitFab = find.byType(FloatingActionButton);
      expect(
        submitFab,
        findsAtLeastNWidgets(1),
        reason: 'Submit FAB should be available',
      );

      // Try to submit by tapping the first FAB (which should be the submit FAB)
      await tester.tap(submitFab.first);
      await tester
          .pump(); // Use pump() instead of pumpAndSettle() for validation

      // Should show validation error
      expect(
        find.text('Category name must be 100 characters or less'),
        findsOneWidget,
      );
    });
  });
}
