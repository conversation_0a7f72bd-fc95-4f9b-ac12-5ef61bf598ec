import 'package:budapp/features/dashboard/presentation/screens/hub_home_screen.dart';
import 'package:budapp/main.dart';
import 'package:budapp/providers/providers.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

import '../helpers/mock_providers.dart';

/// Integration tests for FAB-based navigation system
///
/// These tests verify that:
/// 1. Bottom navigation is completely removed from all screens
/// 2. FAB system is present on all screens with correct visibility
/// 3. Navigation flows work through FAB system
/// 4. Home screen serves as central navigation hub
void main() {
  group('FAB Navigation System Integration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer(
        overrides: MockProviders.authenticatedUserOverrides(),
      );
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('should show FAB system on home screen', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            // Override background initialization to complete immediately
            backgroundInitializationProvider.overrideWith(
              (ref) => Future.value(true),
            ),
          ],
          child: const MyApp(),
        ),
      );

      // Wait for app to load
      await tester.pumpAndSettle();

      // Verify FAB system is present
      expect(
        find.byType(GlobalFabSystem),
        findsOneWidget,
        reason: 'FAB system should be present on home screen',
      );

      // Verify add transaction FAB is present
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be present',
      );
    });

    testWidgets(
      'should show FAB system on all screens with correct visibility',
      (tester) async {
        await tester.pumpWidget(
          ProviderScope(
            overrides: [
              ...MockProviders.authenticatedUserOverrides(),
              // Override background initialization to complete immediately
              backgroundInitializationProvider.overrideWith(
                (ref) => Future.value(true),
              ),
            ],
            child: const MyApp(),
          ),
        );

        await tester.pumpAndSettle();

        // Test home screen FAB visibility
        expect(
          find.byType(GlobalFabSystem),
          findsOneWidget,
          reason: 'FAB system should be present on home screen',
        );

        // Home screen should only show add transaction FAB
        expect(
          find.byIcon(Icons.add),
          findsOneWidget,
          reason: 'Add transaction FAB should be visible on home screen',
        );

        expect(
          find.byIcon(Icons.arrow_back),
          findsNothing,
          reason: 'Back FAB should not be visible on home screen',
        );

        expect(
          find.byIcon(Icons.home),
          findsNothing,
          reason: 'Home FAB should not be visible on home screen',
        );
      },
    );

    testWidgets('should show correct FABs on regular screens', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            // Override background initialization to complete immediately
            backgroundInitializationProvider.overrideWith(
              (ref) => Future.value(true),
            ),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Navigate to accounts screen (regular screen - one step from home)
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();

      // Regular screens should show back FAB and add transaction FAB
      expect(
        find.byIcon(Icons.arrow_back),
        findsOneWidget,
        reason: 'Back FAB should be visible on regular screens',
      );

      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be visible on regular screens',
      );

      expect(
        find.byIcon(Icons.home),
        findsNothing,
        reason: 'Home FAB should not be visible on regular screens',
      );
    });

    testWidgets('should navigate correctly through FAB system', (tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            // Override background initialization to complete immediately
            backgroundInitializationProvider.overrideWith(
              (ref) => Future.value(true),
            ),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify we start on home screen
      expect(
        find.byType(HubHomeScreen),
        findsOneWidget,
        reason: 'Should start on hub home screen',
      );

      // Navigate to accounts via home screen
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();

      // Use back FAB to return to home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      expect(
        find.byType(HubHomeScreen),
        findsOneWidget,
        reason: 'Back FAB should return to home screen',
      );
    });

    testWidgets('should restore navigation FABs after canceling form creation', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: MockProviders.authenticatedUserOverrides(),
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Step 1: Start on home screen - verify add transaction FAB is present
      expect(
        find.byType(HubHomeScreen),
        findsOneWidget,
        reason: 'Should start on hub home screen',
      );
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be present on home screen',
      );

      // Step 2: Navigate to accounts screen - verify navigation FABs are present
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();

      expect(
        find.byIcon(Icons.arrow_back),
        findsOneWidget,
        reason: 'Back FAB should be present on accounts screen',
      );
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be present on accounts screen',
      );

      // Step 3: Navigate to account create screen - verify form FABs are present
      final createButton = find.text('+ Add Account');
      await tester.tap(createButton);
      await tester.pumpAndSettle();

      // Form screens should show form FABs and hide navigation FABs
      expect(
        find.byIcon(Icons.check),
        findsOneWidget,
        reason: 'Save FAB should be present on form screen',
      );
      expect(
        find.byIcon(Icons.close),
        findsOneWidget,
        reason: 'Cancel FAB should be present on form screen',
      );

      // Step 4: Press cancel to go back to accounts screen
      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Step 5: Verify navigation FABs are restored on accounts screen
      expect(
        find.byIcon(Icons.arrow_back),
        findsOneWidget,
        reason: 'Back FAB should be restored after canceling form',
      );
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be restored after canceling form',
      );

      // Step 6: Navigate back to home and verify FABs are still working
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      expect(
        find.byType(HubHomeScreen),
        findsOneWidget,
        reason: 'Should be back on home screen',
      );
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason:
            'Add transaction FAB should be present on home screen after round trip',
      );
    });

    testWidgets('should provide access to all features through home screen', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            // Override background initialization to complete immediately
            backgroundInitializationProvider.overrideWith(
              (ref) => Future.value(true),
            ),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify home screen has all feature cards
      final expectedFeatures = [
        'Accounts',
        'Transactions',
        'Budgets',
        'Goals',
        'Statistics',
        'Settings',
      ];

      for (final feature in expectedFeatures) {
        expect(
          find.text(feature),
          findsOneWidget,
          reason: 'Home screen should have $feature feature card',
        );
      }
    });

    testWidgets('should handle add transaction FAB on all screens', (
      tester,
    ) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            ...MockProviders.authenticatedUserOverrides(),
            // Override background initialization to complete immediately
            backgroundInitializationProvider.overrideWith(
              (ref) => Future.value(true),
            ),
          ],
          child: const MyApp(),
        ),
      );

      await tester.pumpAndSettle();

      // Test add transaction FAB on home screen
      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be present on home screen',
      );

      // Navigate to different screens and verify add transaction FAB is always present
      await tester.tap(find.text('Accounts'));
      await tester.pumpAndSettle();

      expect(
        find.byIcon(Icons.add),
        findsOneWidget,
        reason: 'Add transaction FAB should be present on accounts screen',
      );
    });
  });
}
