import 'package:budapp/data/models/account.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/data/repositories/interfaces/category_repository.dart';
import 'package:budapp/providers/repository_providers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/screens/base_editable_form_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_wrapper.dart';

class MockCategoryRepository extends Mock implements ICategoryRepository {}

void main() {
  group('Form Field State Management Integration Tests', () {
    late MockCategoryRepository mockCategoryRepository;

    setUp(() {
      mockCategoryRepository = MockCategoryRepository();

      // Setup default mock response
      when(() => mockCategoryRepository.getActiveCategories()).thenAnswer(
        (_) async => [
          Category(
            id: 'cat1',
            userId: 'test-user',
            name: 'Food',
            type: CategoryType.expense,
            parentId: null,
            schemaVersion: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
          Category(
            id: 'cat2',
            userId: 'test-user',
            name: 'Transport',
            type: CategoryType.expense,
            parentId: null,
            schemaVersion: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          ),
        ],
      );
    });

    testWidgets(
      'ColorPicker should update UI when selection changes in form context',
      (tester) async {
        String? formSubmittedColor;

        final fieldConfigs = [
          const ColorPickerFieldConfig(
            key: 'color',
            label: 'Account Color',
            availableColors: ['#F44336', '#4CAF50', '#2196F3'],
            initialValue: '#F44336',
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createCategoryTestWidgetWithRouter(
            Scaffold(
              body: SizedBox(
                width: double.infinity,
                height: 600, // Provide bounded height
                child: BaseEditableFormScreen<Map<String, dynamic>>(
                  title: 'Test Form',
                  fieldConfigs: fieldConfigs,
                  onSubmit: (formData) async {
                    formSubmittedColor = formData['color'] as String?;
                  },
                ),
              ),
            ),
            initialLocation: '/forms/test',
            overrides: [
              categoryRepositoryProvider.overrideWithValue(
                mockCategoryRepository,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Find the color picker (first red color should be selected initially)
        final colorContainers = find.byType(Container);
        expect(
          colorContainers,
          findsAtLeastNWidgets(3),
        ); // Should have color options

        // Find and tap the green color option (second color in our test list)
        final gestureDetectors = find.byType(GestureDetector);
        expect(gestureDetectors, findsAtLeastNWidgets(3));

        // Tap the second color (green)
        await tester.tap(gestureDetectors.at(1));
        await tester.pumpAndSettle();

        // Check if the UI updated to show the new selection
        // This test will FAIL if the state management issue exists
        // We should see visual feedback that green is now selected

        // Try to submit the form to see if the value was actually updated
        final submitButton = find.byType(ElevatedButton);
        if (tester.any(submitButton)) {
          await tester.tap(submitButton);
          await tester.pumpAndSettle();

          // This should be '#4CAF50' (green) if state management works
          // It will likely still be '#F44336' (red) if the issue exists
          expect(
            formSubmittedColor,
            equals('#4CAF50'),
            reason:
                'Color picker should update form value when selection changes',
          );
        }
      },
    );

    testWidgets(
      'IconPicker should update UI when selection changes in form context',
      (tester) async {
        String? formSubmittedIcon;

        final fieldConfigs = [
          const IconPickerFieldConfig(
            key: 'icon',
            label: 'Account Icon',
            availableIcons: [
              'account_balance_wallet',
              'savings',
              'credit_card',
            ],
            initialValue: 'account_balance_wallet',
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createCategoryTestWidgetWithRouter(
            Scaffold(
              body: SizedBox(
                width: double.infinity,
                height: 600, // Provide bounded height
                child: BaseEditableFormScreen<Map<String, dynamic>>(
                  title: 'Test Form',
                  fieldConfigs: fieldConfigs,
                  onSubmit: (formData) async {
                    formSubmittedIcon = formData['icon'] as String?;
                  },
                ),
              ),
            ),
            initialLocation: '/forms/test',
            overrides: [
              categoryRepositoryProvider.overrideWithValue(
                mockCategoryRepository,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Find the icon picker containers
        final iconContainers = find.byType(Container);
        expect(iconContainers, findsAtLeastNWidgets(3));

        // Find and tap the second icon (savings)
        final gestureDetectors = find.byType(GestureDetector);
        expect(gestureDetectors, findsAtLeastNWidgets(3));

        await tester.tap(gestureDetectors.at(1));
        await tester.pumpAndSettle();

        // Try to submit the form
        final submitButton = find.byType(ElevatedButton);
        if (tester.any(submitButton)) {
          await tester.tap(submitButton);
          await tester.pumpAndSettle();

          // This should be 'savings' if state management works
          expect(
            formSubmittedIcon,
            equals('savings'),
            reason:
                'Icon picker should update form value when selection changes',
          );
        }
      },
    );

    testWidgets(
      'AccountType selector should update UI when selection changes in form context',
      (tester) async {
        Map<String, dynamic>? formSubmittedAccountType;

        final fieldConfigs = [
          const FormFieldConfig<Map<String, dynamic>>(
            key: 'accountType',
            type: FormFieldType.custom,
            label: 'Account Type',
            initialValue: {
              'type': AccountType.checking,
              'classification': AccountClassification.asset,
            },
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createCategoryTestWidgetWithRouter(
            Scaffold(
              body: SizedBox(
                width: double.infinity,
                height: 600, // Provide bounded height
                child: BaseEditableFormScreen<Map<String, dynamic>>(
                  title: 'Test Form',
                  fieldConfigs: fieldConfigs,
                  onSubmit: (formData) async {
                    formSubmittedAccountType =
                        formData['accountType'] as Map<String, dynamic>?;
                  },
                ),
              ),
            ),
            initialLocation: '/forms/test',
            overrides: [
              categoryRepositoryProvider.overrideWithValue(
                mockCategoryRepository,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Try to find and interact with account type selector
        // Look for savings account option and tap it
        final savingsOption = find.text('Savings');
        if (tester.any(savingsOption)) {
          await tester.tap(savingsOption);
          await tester.pumpAndSettle();

          // Try to submit the form
          final submitButton = find.byType(ElevatedButton);
          if (tester.any(submitButton)) {
            await tester.tap(submitButton);
            await tester.pumpAndSettle();

            // Should show savings account type if state management works
            expect(
              formSubmittedAccountType?['type'],
              equals(AccountType.savings),
              reason:
                  'Account type selector should update form value when selection changes',
            );
          }
        }
      },
    );

    testWidgets(
      'ParentCategory dropdown should populate and allow selection in form context',
      (tester) async {
        String? formSubmittedParentCategory;

        final fieldConfigs = [
          const FormFieldConfig<String?>(
            key: 'parentId', // Use correct key supported by FormFieldFactory
            type: FormFieldType.custom,
            label: 'Parent Category',
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createCategoryTestWidgetWithRouter(
            Scaffold(
              body: SizedBox(
                width: double.infinity,
                height: 600, // Provide bounded height
                child: BaseEditableFormScreen<Map<String, dynamic>>(
                  title: 'Test Form',
                  fieldConfigs: fieldConfigs,
                  onSubmit: (formData) async {
                    formSubmittedParentCategory =
                        formData['parentId'] as String?;
                  },
                ),
              ),
            ),
            initialLocation: '/forms/test',
            overrides: [
              categoryRepositoryProvider.overrideWithValue(
                mockCategoryRepository,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Should load categories and show dropdown
        expect(find.byType(DropdownButtonFormField<String?>), findsOneWidget);

        // Tap dropdown to open it
        await tester.tap(find.byType(DropdownButtonFormField<String?>));
        await tester.pumpAndSettle();

        // Should show category options
        expect(find.text('Food (Expense)'), findsOneWidget);
        expect(find.text('Transport (Expense)'), findsOneWidget);

        // Select Food category
        await tester.tap(find.text('Food (Expense)'));
        await tester.pumpAndSettle();

        // Try to submit the form
        final submitButton = find.byType(ElevatedButton);
        if (tester.any(submitButton)) {
          await tester.tap(submitButton);
          await tester.pumpAndSettle();

          // Should have selected the Food category
          expect(
            formSubmittedParentCategory,
            equals('cat1'),
            reason:
                'Parent category dropdown should allow selection and update form value',
          );
        }
      },
    );

    testWidgets(
      'Form should rebuild and show visual feedback when field values change',
      (tester) async {
        final fieldConfigs = [
          const ColorPickerFieldConfig(
            key: 'color',
            label: 'Color',
            availableColors: ['#F44336', '#4CAF50'],
            initialValue: '#F44336',
          ),
        ];

        await tester.pumpWidget(
          TestWrapper.createCategoryTestWidgetWithRouter(
            Scaffold(
              body: SizedBox(
                width: double.infinity,
                height: 600, // Provide bounded height
                child: BaseEditableFormScreen<Map<String, dynamic>>(
                  title: 'Test Form',
                  fieldConfigs: fieldConfigs,
                  onSubmit: (formData) async {},
                ),
              ),
            ),
            initialLocation: '/forms/test',
            overrides: [
              categoryRepositoryProvider.overrideWithValue(
                mockCategoryRepository,
              ),
            ],
          ),
        );

        await tester.pumpAndSettle();

        // Find the color picker grid (Wrap widget) and its containers
        final colorPickerGrid = find.byType(Wrap);
        expect(colorPickerGrid, findsOneWidget);

        // Find GestureDetectors only within the color picker grid
        final colorPickerGestureDetectors = find.descendant(
          of: colorPickerGrid,
          matching: find.byType(GestureDetector),
        );
        expect(
          colorPickerGestureDetectors,
          findsExactly(2),
        ); // Should be exactly 2 colors

        // Count the number of selected color indicators (Icons.check)
        // Initially, the first color (#F44336) should be selected
        final initialCheckIcons = find.descendant(
          of: colorPickerGrid,
          matching: find.byIcon(Icons.check),
        );
        final initialSelectedCount = initialCheckIcons.evaluate().length;

        expect(initialSelectedCount, equals(1));

        // Tap the second color (green #4CAF50)
        await tester.tap(colorPickerGestureDetectors.at(1));
        await tester.pump(); // Single pump to check immediate rebuild

        // The UI should have updated to show the new selection
        // There should still be exactly one selected color, but it should be the second color

        // Count Icons.check only within the color picker grid after tap
        final afterClickCheckIcons = find.descendant(
          of: colorPickerGrid,
          matching: find.byIcon(Icons.check),
        );
        final afterClickSelectedCount = afterClickCheckIcons.evaluate().length;

        expect(
          afterClickSelectedCount,
          equals(1),
          reason:
              'Form should show exactly one selected color after selection changes',
        );

        // Verify the visual state updated by checking that there's still exactly one check icon in the color picker
        final finalColorPickerCheckIcons = find.descendant(
          of: find.byType(Wrap), // The Wrap widget containing the color grid
          matching: find.byIcon(Icons.check),
        );
        expect(finalColorPickerCheckIcons, findsOneWidget);
      },
    );
  });
}
