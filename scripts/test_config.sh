#!/bin/bash

# BudApp Test Configuration Script
# Provides timeout and performance configurations for different test types

set -e

# Test timeout configurations (in seconds)
UNIT_TEST_TIMEOUT=60
INTEGRATION_TEST_TIMEOUT=180
WIDGET_TEST_TIMEOUT=120
E2E_TEST_TIMEOUT=300

# Performance configurations
OPTIMAL_CONCURRENCY=6
FAST_CONCURRENCY=8
SAFE_CONCURRENCY=4

# Test type detection and timeout assignment
get_test_timeout() {
    local test_path="$1"
    
    if [[ "$test_path" == *"integration"* ]]; then
        echo $INTEGRATION_TEST_TIMEOUT
    elif [[ "$test_path" == *"widget"* ]]; then
        echo $WIDGET_TEST_TIMEOUT
    elif [[ "$test_path" == *"e2e"* ]]; then
        echo $E2E_TEST_TIMEOUT
    else
        echo $UNIT_TEST_TIMEOUT
    fi
}

# Get optimal concurrency based on test type
get_optimal_concurrency() {
    local test_path="$1"
    local system_load="$2"
    
    # Check system load if provided
    if [[ -n "$system_load" && "$system_load" -gt 80 ]]; then
        echo $SAFE_CONCURRENCY
        return
    fi
    
    if [[ "$test_path" == *"integration"* ]]; then
        # Integration tests need more resources per test
        echo $SAFE_CONCURRENCY
    elif [[ "$test_path" == *"unit"* ]]; then
        # Unit tests can run with higher concurrency
        echo $FAST_CONCURRENCY
    else
        echo $OPTIMAL_CONCURRENCY
    fi
}

# Check if tests need special timeout handling
needs_extended_timeout() {
    local test_file="$1"
    
    # Files that typically need longer timeouts
    local long_running_patterns=(
        "firestore_security_rules_test.dart"
        "integration_test.dart"
        "e2e_test.dart"
        "performance_test.dart"
        "repository_integration_test.dart"
        "auth_flow_test.dart"
    )
    
    for pattern in "${long_running_patterns[@]}"; do
        if [[ "$test_file" == *"$pattern"* ]]; then
            return 0  # true
        fi
    done
    
    return 1  # false
}

# Export functions and variables for use in other scripts
export -f get_test_timeout
export -f get_optimal_concurrency
export -f needs_extended_timeout
export UNIT_TEST_TIMEOUT
export INTEGRATION_TEST_TIMEOUT
export WIDGET_TEST_TIMEOUT
export E2E_TEST_TIMEOUT
export OPTIMAL_CONCURRENCY
export FAST_CONCURRENCY
export SAFE_CONCURRENCY