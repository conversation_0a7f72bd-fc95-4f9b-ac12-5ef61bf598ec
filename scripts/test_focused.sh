#!/bin/bash

# BudApp Focused Test Script
# Provides selective test execution strategies for faster development workflow
# Usage: ./scripts/test_focused.sh [strategy] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Load test configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/test_config.sh"

# Configuration
DEFAULT_REPORTER="compact"

# Helper function for colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage information
show_usage() {
    cat << EOF
BudApp Focused Test Script

USAGE:
    ./scripts/test_focused.sh [strategy] [options]

STRATEGIES:
    quick           - Run fastest tests only (unit tests, no integration)
    unit            - Run all unit tests with optimal concurrency
    integration     - Run integration tests only
    feature <name>  - Run tests for specific feature (auth, accounts, etc.)
    file <path>     - Run tests for specific file
    changed         - Run tests for files changed in current branch
    coverage        - Run tests with coverage analysis
    failing         - Re-run only previously failing tests
    smoke           - Run critical path tests only
    all             - Run full test suite with optimizations

OPTIONS:
    --concurrency N - Override concurrency level (default: $OPTIMAL_CONCURRENCY)
    --reporter R    - Override reporter (default: $DEFAULT_REPORTER)
    --timeout N     - Set timeout in seconds
    --verbose       - Enable verbose output
    --no-coverage   - Skip coverage collection
    --watch         - Run in watch mode

EXAMPLES:
    ./scripts/test_focused.sh quick
    ./scripts/test_focused.sh feature auth
    ./scripts/test_focused.sh file test/unit/providers/providers_test.dart
    ./scripts/test_focused.sh unit --concurrency 8
    ./scripts/test_focused.sh coverage --verbose

EOF
}

# Parse command line arguments
STRATEGY=""
CONCURRENCY=$OPTIMAL_CONCURRENCY
REPORTER=$DEFAULT_REPORTER
TIMEOUT=""
VERBOSE=false
NO_COVERAGE=false
WATCH=false
TARGET=""

while [[ $# -gt 0 ]]; do
    case $1 in
        quick|unit|integration|changed|coverage|failing|smoke|all)
            STRATEGY="$1"
            shift
            ;;
        feature|file)
            STRATEGY="$1"
            TARGET="$2"
            shift 2
            ;;
        --concurrency)
            CONCURRENCY="$2"
            shift 2
            ;;
        --reporter)
            REPORTER="$2"
            shift 2
            ;;
        --timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --no-coverage)
            NO_COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Default to quick if no strategy specified
if [[ -z "$STRATEGY" ]]; then
    STRATEGY="quick"
fi

# Build flutter test command with intelligent timeout and concurrency
build_flutter_command() {
    local test_path="$1"
    local cmd="flutter test"
    
    if [[ -n "$test_path" ]]; then
        cmd="$cmd $test_path"
    fi
    
    # Use intelligent concurrency if not explicitly set
    if [[ "$CONCURRENCY" == "$OPTIMAL_CONCURRENCY" ]]; then
        CONCURRENCY=$(get_optimal_concurrency "$test_path")
    fi
    
    cmd="$cmd --concurrency=$CONCURRENCY --reporter=$REPORTER"
    
    if [[ "$STRATEGY" == "coverage" && "$NO_COVERAGE" != "true" ]]; then
        cmd="$cmd --coverage"
    fi
    
    # Use intelligent timeout if not explicitly set
    if [[ -z "$TIMEOUT" ]]; then
        TIMEOUT=$(get_test_timeout "$test_path")
    fi
    
    if [[ -n "$TIMEOUT" ]]; then
        cmd="timeout ${TIMEOUT}s $cmd"
    fi
    
    if [[ "$WATCH" == "true" ]]; then
        # Note: flutter test doesn't have built-in watch, but we can simulate
        print_warning "Watch mode not directly supported by flutter test"
        print_status "Consider using: fswatch -o lib/ test/ | xargs -n1 -I{} $cmd"
    fi
    
    echo "$cmd"
}

# Execute strategy
case "$STRATEGY" in
    "quick")
        print_status "Running quick tests (unit tests only, optimized)"
        CMD=$(build_flutter_command "test/unit/")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Quick tests completed"
        ;;
        
    "unit")
        print_status "Running all unit tests with optimal concurrency"
        CMD=$(build_flutter_command "test/unit/")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Unit tests completed"
        ;;
        
    "integration")
        print_status "Running integration tests"
        CMD=$(build_flutter_command "test/integration/")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Integration tests completed"
        ;;
        
    "feature")
        if [[ -z "$TARGET" ]]; then
            print_error "Feature name required. Usage: ./scripts/test_focused.sh feature <name>"
            exit 1
        fi
        print_status "Running tests for feature: $TARGET"
        
        # Look for tests in multiple locations
        TEST_PATHS=""
        for path in "test/unit/features/$TARGET/" "test/integration/features/$TARGET/" "test/unit/$TARGET/" "test/integration/$TARGET/" "test/features/$TARGET/"; do
            if [[ -d "$path" ]]; then
                TEST_PATHS="$TEST_PATHS $path"
            fi
        done
        
        if [[ -z "$TEST_PATHS" ]]; then
            print_error "No tests found for feature: $TARGET"
            print_status "Searched in: test/unit/features/$TARGET/, test/integration/features/$TARGET/, test/unit/$TARGET/, test/integration/$TARGET/, test/features/$TARGET/"
            exit 1
        fi
        
        CMD=$(build_flutter_command "$TEST_PATHS")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Feature tests completed for: $TARGET"
        ;;
        
    "file")
        if [[ -z "$TARGET" ]]; then
            print_error "File path required. Usage: ./scripts/test_focused.sh file <path>"
            exit 1
        fi
        print_status "Running tests for file: $TARGET"
        
        if [[ ! -f "$TARGET" ]]; then
            print_error "Test file not found: $TARGET"
            exit 1
        fi
        
        CMD=$(build_flutter_command "$TARGET")
        print_status "Command: $CMD"
        eval $CMD
        print_success "File tests completed for: $TARGET"
        ;;
        
    "changed")
        print_status "Running tests for changed files"
        
        # Get changed files in current branch vs main
        CHANGED_FILES=$(git diff --name-only main...HEAD | grep -E '\.(dart)$' | grep -v test/ || true)
        
        if [[ -z "$CHANGED_FILES" ]]; then
            print_warning "No changed Dart files found"
            exit 0
        fi
        
        print_status "Changed files:"
        echo "$CHANGED_FILES" | sed 's/^/  /'
        
        # Find corresponding test files
        TEST_FILES=""
        for file in $CHANGED_FILES; do
            # Convert lib/features/auth/file.dart to test/unit/features/auth/file_test.dart
            test_file=$(echo "$file" | sed 's|^lib/|test/unit/|' | sed 's|\.dart$|_test.dart|')
            if [[ -f "$test_file" ]]; then
                TEST_FILES="$TEST_FILES $test_file"
            fi
        done
        
        if [[ -z "$TEST_FILES" ]]; then
            print_warning "No corresponding test files found for changed files"
            exit 0
        fi
        
        CMD=$(build_flutter_command "$TEST_FILES")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Changed file tests completed"
        ;;
        
    "coverage")
        print_status "Running tests with coverage analysis"
        CMD=$(build_flutter_command "")
        print_status "Command: $CMD"
        eval $CMD
        
        if [[ "$NO_COVERAGE" != "true" ]]; then
            print_status "Generating coverage report..."
            genhtml coverage/lcov.info -o coverage/html --ignore-errors source > /dev/null 2>&1 || print_warning "genhtml not available, skipping HTML report"
            lcov --list coverage/lcov.info | tail -1
        fi
        print_success "Coverage tests completed"
        ;;
        
    "failing")
        print_status "Re-running previously failing tests"
        print_warning "This strategy requires storing previous test results"
        print_status "Running full test suite to identify any current failures"
        CMD=$(build_flutter_command "")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Test run completed"
        ;;
        
    "smoke")
        print_status "Running smoke tests (critical path)"
        # Define critical test files
        SMOKE_TESTS="test/unit/providers/providers_test.dart test/unit/services/auth_service_test.dart test/integration/auth_flow_test.dart"
        
        # Filter to only existing files
        EXISTING_SMOKE_TESTS=""
        for test in $SMOKE_TESTS; do
            if [[ -f "$test" ]]; then
                EXISTING_SMOKE_TESTS="$EXISTING_SMOKE_TESTS $test"
            fi
        done
        
        if [[ -z "$EXISTING_SMOKE_TESTS" ]]; then
            print_error "No smoke test files found"
            exit 1
        fi
        
        CMD=$(build_flutter_command "$EXISTING_SMOKE_TESTS")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Smoke tests completed"
        ;;
        
    "all")
        print_status "Running full test suite with optimizations"
        CMD=$(build_flutter_command "")
        print_status "Command: $CMD"
        eval $CMD
        print_success "Full test suite completed"
        ;;
        
    *)
        print_error "Unknown strategy: $STRATEGY"
        show_usage
        exit 1
        ;;
esac

# Show performance info if verbose
if [[ "$VERBOSE" == "true" ]]; then
    print_status "Test execution completed with concurrency=$CONCURRENCY, reporter=$REPORTER"
fi