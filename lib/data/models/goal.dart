import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'goal.freezed.dart';
part 'goal.g.dart';

/// Goal status for tracking goal lifecycle
enum GoalStatus {
  @JsonValue('active')
  active, // Goal is actively being tracked
  @JsonValue('paused')
  paused, // Goal is temporarily paused
  @JsonValue('completed')
  completed, // Goal has been achieved
  @JsonValue('cancelled')
  cancelled, // Goal was cancelled
}

/// Financial goal data model
@freezed
sealed class Goal with _$Goal {
  const factory Goal({
    @Default('') String id, // Unique goal identifier
    @Default('') String userId, // Owner user ID
    @Default('') String name, // Goal name/title (2-100 chars)
    String? description, // Optional description (max 500 chars)
    @Default(0) int targetAmountCents, // Target amount in cents
    @Default(0)
    int currentAmountCents, // Current progress amount in cents (denormalized)
    DateTime? targetDate, // Optional target completion date
    @Default(false)
    bool isCompleted, // Completion status (for backward compatibility)
    String? colorHex, // Hex color code (#RRGGBB format)
    String? iconName, // Icon identifier/name
    @Default(GoalStatus.active) GoalStatus status, // Current goal status
    @Default(true) bool isActive, // Soft delete flag
    @Default(1) int schemaVersion, // Schema version for migrations
    DateTime? createdAt, // Creation timestamp
    DateTime? updatedAt, // Last update timestamp
    @Default({}) Map<String, dynamic> metadata, // Additional metadata
  }) = _Goal;

  const Goal._();

  factory Goal.fromJson(Map<String, dynamic> json) =>
      Goal.fromJsonWithCompatibility(json);

  /// Create from JSON with backward compatibility
  factory Goal.fromJsonWithCompatibility(Map<String, dynamic> json) {
    // Defensive copy to avoid mutating the original map
    final convertedJson = Map<String, dynamic>.from(json);

    // Handle backward compatibility for missing status field
    // If status is missing but isCompleted is true, set status to completed
    // If status is missing and isCompleted is false/missing, set status to active
    if (!convertedJson.containsKey('status')) {
      final isCompleted = convertedJson['isCompleted'] as bool? ?? false;
      convertedJson['status'] = isCompleted ? 'completed' : 'active';
    }

    // Handle missing schemaVersion for legacy data
    if (convertedJson['schemaVersion'] == null) {
      convertedJson['schemaVersion'] = 1;
    }

    return _$GoalFromJson(convertedJson);
  }

  /// Create from Firestore DocumentSnapshot
  factory Goal.fromFirestore(DocumentSnapshot<Map<String, dynamic>> doc) {
    final data = doc.data();
    if (data == null) {
      throw Exception('Goal document data is null');
    }

    // Handle Firestore Timestamp conversion and backward compatibility
    final convertedData = Map<String, dynamic>.from(data);

    // Handle backward compatibility for missing status field
    // If status is missing but isCompleted is true, set status to completed
    if (!convertedData.containsKey('status') &&
        convertedData['isCompleted'] == true) {
      convertedData['status'] = 'completed';
    }

    // Convert Firestore Timestamps to DateTime strings
    if (convertedData['createdAt'] is Timestamp) {
      convertedData['createdAt'] = (convertedData['createdAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }
    if (convertedData['updatedAt'] is Timestamp) {
      convertedData['updatedAt'] = (convertedData['updatedAt'] as Timestamp)
          .toDate()
          .toIso8601String();
    }
    if (convertedData['targetDate'] is Timestamp) {
      convertedData['targetDate'] = (convertedData['targetDate'] as Timestamp)
          .toDate()
          .toIso8601String();
    }

    // Ensure document ID matches the id field
    convertedData['id'] = doc.id;

    return Goal.fromJsonWithCompatibility(convertedData);
  }

  /// Create a new goal
  factory Goal.create({
    required String userId,
    required String name,
    String? description,
    required int targetAmountCents,
    int currentAmountCents = 0,
    DateTime? targetDate,
    String? colorHex,
    String? iconName,
    GoalStatus status = GoalStatus.active,
  }) {
    final now = DateTime.now();
    return Goal(
      id: '', // Will be set by repository
      userId: userId,
      name: name,
      description: description,
      targetAmountCents: targetAmountCents,
      currentAmountCents: currentAmountCents,
      targetDate: targetDate,
      isCompleted: status == GoalStatus.completed,
      colorHex: colorHex,
      iconName: iconName,
      status: status,
      isActive: true,
      schemaVersion: 1,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// Convert to Firestore document data
  Map<String, dynamic> toFirestore() {
    final data = toJson();

    // Convert DateTime to Firestore Timestamp for server storage
    if (data['createdAt'] is String) {
      data['createdAt'] = Timestamp.fromDate(
        DateTime.parse(data['createdAt'] as String),
      );
    }
    if (data['updatedAt'] is String) {
      data['updatedAt'] = Timestamp.fromDate(
        DateTime.parse(data['updatedAt'] as String),
      );
    }
    if (data['targetDate'] is String) {
      data['targetDate'] = Timestamp.fromDate(
        DateTime.parse(data['targetDate'] as String),
      );
    }

    return data;
  }

  /// Generate a new goal with updated timestamp
  Goal updated() => copyWith(updatedAt: DateTime.now());

  /// Calculate progress percentage (0.0 to 1.0)
  double get progressPercentage {
    if (targetAmountCents <= 0) return 0;
    final progress = currentAmountCents / targetAmountCents;
    return progress.clamp(0, 1);
  }

  /// Calculate remaining amount in cents
  int get remainingAmountCents {
    final remaining = targetAmountCents - currentAmountCents;
    return remaining > 0 ? remaining : 0;
  }

  /// Check if goal is achieved (current >= target)
  bool get isAchieved => currentAmountCents >= targetAmountCents;

  /// Check if goal has a deadline
  bool get hasDeadline => targetDate != null;

  /// Check if goal is overdue (past target date and not completed)
  bool get isOverdue {
    if (targetDate == null || isCompleted) return false;
    return DateTime.now().isAfter(targetDate!);
  }

  /// Get days remaining until target date (null if no target date)
  int? get daysRemaining {
    if (targetDate == null) return null;
    final now = DateTime.now();
    final difference = targetDate!.difference(now).inDays;
    return difference > 0 ? difference : 0;
  }

  /// Calculate required daily savings to meet target (null if no target date)
  int? get requiredDailySavingsCents {
    if (targetDate == null || isAchieved) return null;
    final days = daysRemaining;
    if (days == null || days <= 0) return null;
    return (remainingAmountCents / days).ceil();
  }
}

/// Goal validation extension
extension GoalValidation on Goal {
  /// Validates the goal data
  List<String> validate() {
    final errors = <String>[];

    // Name validation
    if (name.trim().isEmpty) {
      errors.add('Goal name is required');
    } else if (name.trim().length < 2) {
      errors.add('Goal name must be at least 2 characters');
    } else if (name.length > 100) {
      errors.add('Goal name must be 100 characters or less');
    }

    // Description validation
    if (description != null && description!.length > 500) {
      errors.add('Goal description must be 500 characters or less');
    }

    // Target amount validation
    if (targetAmountCents <= 0) {
      errors.add('Goal target amount must be greater than 0');
    }

    // Current amount validation
    if (currentAmountCents < 0) {
      errors.add('Goal current amount cannot be negative');
    }

    // Target date validation
    if (targetDate != null) {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final target = DateTime(
        targetDate!.year,
        targetDate!.month,
        targetDate!.day,
      );

      if (target.isBefore(today)) {
        errors.add('Goal target date cannot be in the past');
      }
    }

    // Color validation
    if (colorHex != null && colorHex!.isNotEmpty) {
      final hexColorRegex = RegExp(r'^#[0-9A-Fa-f]{6}$');
      if (!hexColorRegex.hasMatch(colorHex!)) {
        errors.add('Goal color must be a valid hex color (e.g., #FF0000)');
      }
    }

    // Icon validation
    if (iconName != null && iconName!.isNotEmpty) {
      final iconNameRegex = RegExp(r'^[a-zA-Z][a-zA-Z0-9_]*$');
      if (!iconNameRegex.hasMatch(iconName!)) {
        errors.add(
          'Goal icon name must be a valid identifier (letters, numbers, underscores, starting with letter)',
        );
      }
    }

    // Status consistency validation
    if (isCompleted && status != GoalStatus.completed) {
      errors.add('Goal completion status is inconsistent');
    }

    return errors;
  }

  /// Checks if the goal is valid
  bool get isValid => validate().isEmpty;

  /// Gets the status as a string for display
  String get statusString {
    switch (status) {
      case GoalStatus.active:
        return 'Active';
      case GoalStatus.paused:
        return 'Paused';
      case GoalStatus.completed:
        return 'Completed';
      case GoalStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Gets a user-friendly progress message
  String get progressMessage {
    if (isAchieved) {
      return 'Goal achieved!';
    }

    final percentage = (progressPercentage * 100).toStringAsFixed(1);
    return '$percentage% complete';
  }
}
