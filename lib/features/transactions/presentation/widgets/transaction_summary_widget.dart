import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A widget that displays income, expense, and net amount summary for filtered transactions
class TransactionSummaryWidget extends ConsumerWidget {
  const TransactionSummaryWidget({
    super.key,
    this.categoryId,
    this.accountId,
  });

  /// Optional category ID to filter transactions by
  final String? categoryId;

  /// Optional account ID to filter transactions by
  final String? accountId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final currencyFormatter = ref.watch(currencyFormatterProvider);

    // Get filtered transactions summary
    final summaryAsync = ref.watch(
      transactionSummaryProvider(
        (categoryId: categoryId, accountId: accountId),
      ),
    );

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(DesignTokens.spacing16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: summaryAsync.when(
        data: (summary) => _buildSummaryContent(
          context,
          theme,
          l10n,
          currencyFormatter,
          summary,
        ),
        loading: () => const SizedBox(
          height: 60,
          child: Center(
            child: SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
        ),
        error: (error, stack) => SizedBox(
          height: 60,
          child: Center(
            child: Text(
              l10n.errorLoadingData,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryContent(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
    CurrencyFormatter currencyFormatter,
    TransactionSummary summary,
  ) {
    return Row(
      children: [
        // Income section
        Expanded(
          child: _buildSummaryItem(
            icon: Icons.trending_up,
            iconColor: theme.colorScheme.primary,
            label: 'Income',
            amount: currencyFormatter.formatAmount(
              summary.totalIncomeAmountCents,
            ),
            theme: theme,
          ),
        ),

        const SizedBox(width: DesignTokens.spacing16),

        // Expense section
        Expanded(
          child: _buildSummaryItem(
            icon: Icons.trending_down,
            iconColor: theme.colorScheme.error,
            label: 'Expenses',
            amount: currencyFormatter.formatAmount(
              summary.totalExpenseAmountCents,
            ),
            theme: theme,
          ),
        ),

        const SizedBox(width: DesignTokens.spacing16),

        // Net amount section
        Expanded(
          child: _buildSummaryItem(
            icon: summary.netAmountCents >= 0
                ? Icons.account_balance
                : Icons.account_balance_wallet,
            iconColor: summary.netAmountCents >= 0
                ? theme.colorScheme.primary
                : theme.colorScheme.error,
            label: 'Net',
            amount: currencyFormatter.formatAmount(summary.netAmountCents),
            theme: theme,
            isNet: true,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String amount,
    required ThemeData theme,
    bool isNet = false,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: iconColor,
          size: 20,
        ),
        const SizedBox(height: DesignTokens.spacing4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: DesignTokens.spacing2),
        Text(
          amount,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isNet
                ? (amount.startsWith('-')
                      ? theme.colorScheme.error
                      : theme.colorScheme.primary)
                : theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

/// Data class for transaction summary
class TransactionSummary {
  const TransactionSummary({
    required this.totalIncomeAmountCents,
    required this.totalExpenseAmountCents,
    required this.netAmountCents,
    required this.transactionCount,
  });

  final int totalIncomeAmountCents;
  final int totalExpenseAmountCents;
  final int netAmountCents;
  final int transactionCount;
}
