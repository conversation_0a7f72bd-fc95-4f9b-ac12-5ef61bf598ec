import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for creating a new transaction
class TransactionCreateScreen extends ConsumerStatefulWidget {
  const TransactionCreateScreen({super.key});

  @override
  ConsumerState<TransactionCreateScreen> createState() =>
      _TransactionCreateScreenState();
}

class _TransactionCreateScreenState
    extends ConsumerState<TransactionCreateScreen> {
  Transaction? _currentTransaction;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Watch the transaction creation state
    final createState = ref.watch(transactionCreatorProvider);

    final scaffold = Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: l10n.addTransaction,
        automaticallyImplyLeading: false, // Use global FAB back button instead
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => context.pop(),
            tooltip: l10n.close,
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Progress indicator when creating transaction
            if (createState.isLoading)
              LinearProgressIndicator(
                backgroundColor: theme.colorScheme.surfaceContainerHighest,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),

            // Main form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Builder(
                  builder: (context) {
                    return TransactionForm(
                      onSubmit: (transaction) async {
                        // Store transaction for form FAB usage
                        _currentTransaction = transaction;

                        // Handle submission
                        await _handleTransactionSubmission(
                          transaction,
                          context,
                        );
                      },
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );

    return scaffold.withFormFabs(
      mode: FabMode.formCreate,
      onSave: createState.isLoading ? null : _submitForm,
      onCancel: () => context.pop(),
      isLoading: createState.isLoading,
    );
  }

  /// Handle transaction submission with proper error handling
  Future<void> _handleTransactionSubmission(
    Transaction transaction,
    BuildContext context,
  ) async {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Create the transaction
    await ref
        .read(transactionCreatorProvider.notifier)
        .createTransaction(transaction);

    // Check if creation was successful
    final state = ref.read(transactionCreatorProvider);
    if (state.hasValue && !state.hasError) {
      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.transactionCreated),
            backgroundColor: theme.colorScheme.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate back
        context.pop();
      }
    } else if (state.hasError) {
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.transactionCreateError),
            backgroundColor: theme.colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Submit the form by using the stored transaction
  Future<void> _submitForm() async {
    if (_currentTransaction != null) {
      await _handleTransactionSubmission(_currentTransaction!, context);
    }
  }
}
