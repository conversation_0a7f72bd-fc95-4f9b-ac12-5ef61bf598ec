import 'package:budapp/data/models/transaction.dart';
import 'package:budapp/features/auth/providers/auth_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_form.dart';
import 'package:budapp/features/transactions/presentation/widgets/transaction_metadata_card.dart';
import 'package:budapp/features/transactions/providers/transaction_providers.dart';
import 'package:budapp/features/transactions/services/transaction_error_service.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen for editing an existing transaction
class TransactionEditScreen extends ConsumerStatefulWidget {
  const TransactionEditScreen({super.key, required this.transactionId});
  final String transactionId;

  @override
  ConsumerState<TransactionEditScreen> createState() =>
      _TransactionEditScreenState();
}

class _TransactionEditScreenState extends ConsumerState<TransactionEditScreen> {
  Transaction? _currentTransaction;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Watch for the specific transaction
    final transactionsAsync = ref.watch(transactionListProvider);
    final updateState = ref.watch(transactionUpdaterProvider);

    final scaffold = Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: l10n.editTransaction,
        automaticallyImplyLeading: false, // Use global FAB back button instead
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => context.pop(),
            tooltip: l10n.close,
          ),
        ],
      ),
      body: SafeArea(
        child: transactionsAsync.when(
          data: (transactions) {
            final transactionIndex = transactions.indexWhere(
              (t) => t.id == widget.transactionId,
            );

            if (transactionIndex == -1) {
              return _buildNotFoundState(context, theme, l10n);
            }

            final transaction = transactions[transactionIndex];
            return _buildEditForm(
              context,
              ref,
              transaction,
              updateState,
              theme,
              l10n,
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) =>
              _buildErrorState(context, ref, error, theme, l10n),
        ),
      ),
    );

    return scaffold.withFormFabs(
      mode: FabMode.formEdit,
      onSave: updateState.isLoading ? null : _submitForm,
      onCancel: () => context.pop(),
      onDelete: () => _deleteTransaction(widget.transactionId),
      isLoading: updateState.isLoading,
    );
  }

  Widget _buildEditForm(
    BuildContext context,
    WidgetRef ref,
    Transaction transaction,
    AsyncValue<void> updateState,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return SafeArea(
      child: Column(
        children: [
          // Progress indicator when updating transaction
          if (updateState.isLoading)
            LinearProgressIndicator(
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                theme.colorScheme.primary,
              ),
            ),

          // Main form content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Transaction form
                  TransactionForm(
                    initialTransaction: transaction,
                    onSubmit: (updatedTransaction) async {
                      // Store transaction for form FAB usage
                      _currentTransaction = updatedTransaction;

                      // Handle submission
                      await _handleTransactionUpdate(
                        updatedTransaction,
                        context,
                      );
                    },
                  ),

                  const SizedBox(height: 16),

                  // Metadata card with delete functionality
                  TransactionMetadataCard(
                    transaction: transaction,
                    onDelete: () => _showDeleteConfirmation(
                      context,
                      ref,
                      transaction,
                      theme,
                      l10n,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotFoundState(
    BuildContext context,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(l10n.transactionNotFound, style: theme.textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(
            l10n.transactionMayHaveBeenDeleted,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () => context.pop(),
            child: Text(l10n.goBack),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(
    BuildContext context,
    WidgetRef ref,
    Object error,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
          const SizedBox(height: 16),
          Text(
            l10n.errorLoadingTransaction,
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          FilledButton(
            onPressed: () => ref.invalidate(transactionListProvider),
            child: Text(l10n.retry),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    Transaction transaction,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteTransaction),
        content: Text(l10n.deleteTransactionConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    ).then((confirmed) async {
      if ((confirmed ?? false) && context.mounted) {
        // Show loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 16),
                Text(l10n.deletingTransaction),
              ],
            ),
            duration: const Duration(seconds: 30),
          ),
        );

        // Navigate back immediately for better UX
        context.pop();

        // Perform the deletion in background
        try {
          await ref
              .read(transactionDeleterProvider.notifier)
              .deleteTransaction(transaction.userId, transaction.id);
        } on Exception catch (error) {
          // Show error message if deletion fails
          if (context.mounted) {
            final errorMessage = TransactionErrorService.getDeleteErrorMessage(
              error,
              l10n,
            );
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: theme.colorScheme.error,
                behavior: SnackBarBehavior.floating,
                action: TransactionErrorService.isRetryable(error)
                    ? SnackBarAction(
                        label: l10n.retry,
                        textColor: theme.colorScheme.onError,
                        onPressed: () => _showDeleteConfirmation(
                          context,
                          ref,
                          transaction,
                          theme,
                          l10n,
                        ),
                      )
                    : null,
              ),
            );
          }
        }
      }
    });
  }

  /// Submit the form by using the stored transaction
  Future<void> _submitForm() async {
    if (_currentTransaction != null) {
      await _handleTransactionUpdate(_currentTransaction!, context);
    }
  }

  /// Handle transaction update with proper error handling
  Future<void> _handleTransactionUpdate(
    Transaction transaction,
    BuildContext context,
  ) async {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Update the transaction
    await ref
        .read(transactionUpdaterProvider.notifier)
        .updateTransaction(widget.transactionId, transaction);

    // Check if update was successful
    final state = ref.read(transactionUpdaterProvider);
    if (state.hasValue && !state.hasError) {
      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.transactionUpdated),
            backgroundColor: theme.colorScheme.primary,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // Navigate back
        context.pop();
      }
    } else if (state.hasError) {
      // Show error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.transactionUpdateError),
            backgroundColor: theme.colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// Delete the transaction
  Future<void> _deleteTransaction(String transactionId) async {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.deleteTransaction),
        content: Text(l10n.deleteTransactionConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );

    if (confirmed ?? false) {
      // Get current user for deletion
      final authService = ref.read(authServiceProvider);
      final user = authService.currentUser;
      if (user == null) return;

      // Delete the transaction
      await ref
          .read(transactionDeleterProvider.notifier)
          .deleteTransaction(user.uid, transactionId);

      // Check if deletion was successful
      final state = ref.read(transactionDeleterProvider);
      if (state.hasValue && !state.hasError) {
        // Show success message and navigate back
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.transactionDeleted),
              backgroundColor: theme.colorScheme.primary,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // Navigate back
          context.pop();
        }
      } else if (state.hasError) {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(l10n.transactionDeleteError),
              backgroundColor: theme.colorScheme.error,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    }
  }
}
