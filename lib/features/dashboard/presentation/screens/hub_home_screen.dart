import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/widgets/common/overflow_aware_card.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:budapp/widgets/responsive/smart_grid_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// New hub-style home screen with feature cards
class HubHomeScreen extends ConsumerWidget {
  const HubHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(context, theme),

            // Feature cards grid
            Expanded(
              child: _buildFeatureGrid(context, theme),
            ),

            // Pro tip section
            _buildProTip(context, theme),

            // Bottom padding for FAB
            const SizedBox(height: 80),
          ],
        ),
      ),
    ).withGlobalFabs(
      showBackFab: false,
      showHomeFab: false,
      showAddTransactionFab: true,
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Column(
        children: [
          Text(
            'BudApp',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: AppTypography.fontWeightBold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            'Your Budget Assistant 🤖',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureGrid(BuildContext context, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppSpacing.lg),
      child: SmartGridView(
        context: 'feature',
        children: [
          OverflowAwareFeatureCard(
            icon: Icons.account_balance_wallet,
            iconColor: theme.colorScheme.primary,
            title: 'Accounts',
            subtitle: 'Manage your financial accounts',
            actionText: 'Track balances',
            onTap: () => context.push('/accounts'),
          ),
          OverflowAwareFeatureCard(
            icon: Icons.receipt_long,
            iconColor: Colors.blue,
            title: 'Transactions',
            subtitle: 'View your transaction history',
            actionText: 'Recent activity',
            onTap: () => context.push('/transactions'),
          ),
          OverflowAwareFeatureCard(
            icon: Icons.trending_up,
            iconColor: Colors.red,
            title: 'Budgets',
            subtitle: 'Track your spending goals',
            actionText: 'Monthly budgets',
            onTap: () => context.push('/budgets'),
          ),
          OverflowAwareFeatureCard(
            icon: Icons.track_changes,
            iconColor: Colors.purple,
            title: 'Goals',
            subtitle: 'Track your savings goals',
            actionText: 'Progress tracking',
            onTap: () => context.push('/goals'),
          ),
          OverflowAwareFeatureCard(
            icon: Icons.bar_chart,
            iconColor: Colors.orange,
            title: 'Statistics',
            subtitle: 'Analyze spending patterns',
            actionText: 'Insights & charts',
            onTap: () => _showComingSoon(context),
          ),
          OverflowAwareFeatureCard(
            icon: Icons.settings,
            iconColor: Colors.grey,
            title: 'Settings',
            subtitle: 'Profile and app settings',
            actionText: 'Preferences',
            onTap: () => context.push('/profile'),
          ),
        ],
      ),
    );
  }

  Widget _buildProTip(BuildContext context, ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(AppSpacing.lg),
      padding: const EdgeInsets.all(AppSpacing.lg),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppBorderRadius.lg),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: AppSpacing.md),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Pro Tip',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: AppTypography.fontWeightSemiBold,
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                Text(
                  'Start by setting up your accounts, then create budgets to track your spending goals!',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Statistics feature coming soon!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
