import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/utils/color_utils.dart';
import 'package:budapp/utils/icon_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Card widget displaying account information in a list
class AccountCard extends ConsumerWidget {
  const AccountCard({
    super.key,
    required this.account,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onSetPrimary,
    this.onDeactivate,
    this.showActions = false,
  });
  final Account account;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onSetPrimary;
  final VoidCallback? onDeactivate;
  final bool showActions;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final currencyFormatter = ref.watch(currencyFormatterProvider);
    final accountColor = _getAccountColor() ?? theme.colorScheme.primary;

    return Card(
      elevation: 2,
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          // Light shade background based on account icon color
          color: accountColor.withValues(alpha: 0.05),
          border: Border.all(
            color: accountColor.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          child: Padding(
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left: Icon with primary label underneath
                Column(
                  children: [
                    Icon(
                      _getAccountIcon(),
                      color: accountColor,
                      size: 32,
                    ),
                    if (account.isPrimary) ...[
                      const SizedBox(height: AppSpacing.xs),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: AppSpacing.xs,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.success.withValues(alpha: 0.15),
                          borderRadius: BorderRadius.circular(
                            AppBorderRadius.xs,
                          ),
                        ),
                        child: Text(
                          l10n.primary.toUpperCase(),
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: AppColors.success,
                            fontWeight: FontWeight.bold,
                            fontSize: 9,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(width: AppSpacing.md),

                // Center: Account name and description
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Account name with inactive status
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              account.name,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: AppTypography.fontWeightSemiBold,
                                color: theme.colorScheme.onSurface,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (!account.isActive) ...[
                            const SizedBox(width: AppSpacing.xs),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: AppSpacing.sm,
                                vertical: AppSpacing.xs,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    theme.colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(
                                  AppBorderRadius.sm,
                                ),
                              ),
                              child: Text(
                                l10n.inactive,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                  fontWeight: AppTypography.fontWeightMedium,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      // Description on second line (if available)
                      if (account.description?.isNotEmpty ?? false) ...[
                        const SizedBox(height: AppSpacing.xs),
                        Text(
                          account.description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(width: AppSpacing.sm),

                // Right: Amount with currency and edit button
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Edit button in top right corner
                    IconButton(
                      onPressed: onEdit,
                      icon: Icon(
                        Icons.edit_outlined,
                        size: 18,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      iconSize: 18,
                      visualDensity: VisualDensity.compact,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                    const SizedBox(height: AppSpacing.xs),

                    // Current balance with currency
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        // Currency symbol
                        Text(
                          currencyFormatter.symbol,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        // Amount
                        Text(
                          _formatBalanceWithoutCurrency(
                            account.currentBalanceCents,
                          ),
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: AppTypography.fontWeightSemiBold,
                            color: _getBalanceColor(theme),
                          ),
                          textAlign: TextAlign.end,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getAccountIcon() {
    // Use account-specific icon if available
    if (account.iconName?.isNotEmpty ?? false) {
      return iconFromName(account.iconName!);
    }

    // Fallback to type-based icons
    switch (account.type) {
      case AccountType.checking:
        return Icons.account_balance;
      case AccountType.savings:
        return Icons.savings;
      case AccountType.creditCard:
        return Icons.credit_card;
      case AccountType.cash:
        return Icons.account_balance_wallet;
      case AccountType.investment:
        return Icons.trending_up;
      case AccountType.loan:
        return Icons.account_balance_outlined;
    }
  }

  Color? _getAccountColor() {
    if (account.colorHex?.isNotEmpty ?? false) {
      try {
        return parseHex(account.colorHex!);
      } on Exception {
        // Invalid color hex, use default
      }
    }

    // Default colors based on account type
    switch (account.type) {
      case AccountType.checking:
        return AppColors.primary;
      case AccountType.savings:
        return AppColors.success;
      case AccountType.creditCard:
        return AppColors.warning;
      case AccountType.cash:
        return AppColors.info;
      case AccountType.investment:
        return AppColors.tertiary;
      case AccountType.loan:
        return AppColors.error;
    }
  }

  Color _getBalanceColor(ThemeData theme) {
    if (account.classification == AccountClassification.liability) {
      // For liabilities, positive balance is debt (red), negative is overpayment (green)
      return account.currentBalanceCents > 0
          ? AppColors.error
          : AppColors.success;
    } else {
      // For assets, positive balance is good (green), negative is concerning (red)
      return account.currentBalanceCents >= 0
          ? theme.colorScheme.onSurface
          : AppColors.error;
    }
  }

  String _formatBalanceWithoutCurrency(int cents) {
    final amount = cents / 100;
    return amount.toStringAsFixed(2);
  }
}
