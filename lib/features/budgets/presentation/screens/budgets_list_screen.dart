import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/budget.dart';
import 'package:budapp/data/models/category.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_overview_card.dart';
import 'package:budapp/features/budgets/presentation/widgets/budget_progress_bar.dart';
import 'package:budapp/features/budgets/presentation/widgets/category_budget_card.dart';
import 'package:budapp/features/budgets/providers/budget_providers.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/providers/currency_providers.dart';
import 'package:budapp/widgets/common/error_display.dart';
import 'package:budapp/widgets/common/loading_indicator.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Main budgets list screen displaying user's budgets with progress indicators
class BudgetsListScreen extends ConsumerStatefulWidget {
  const BudgetsListScreen({super.key});

  @override
  ConsumerState<BudgetsListScreen> createState() => _BudgetsListScreenState();
}

class _BudgetsListScreenState extends ConsumerState<BudgetsListScreen> {
  bool _isEditMode = false;
  final Map<String, double> _editedBudgetAmounts = <String, double>{};
  final Map<String, TextEditingController> _budgetControllers =
      <String, TextEditingController>{};

  @override
  void dispose() {
    // Dispose all text controllers
    for (final controller in _budgetControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  /// Gets the title string based on current mode
  String _getTitleString(AppLocalizations l10n) {
    if (_isEditMode) {
      return '${l10n.budgets} - Edit Mode';
    } else {
      return l10n.budgets;
    }
  }

  /// Builds the app bar for the budgets screen with conditional TimePeriodSelector
  Widget _buildBudgetsAppBar(AppLocalizations l10n) {
    // For edit mode, use standard app bar without TimePeriodSelector
    if (_isEditMode) {
      return AppBar(
        title: Text(_getTitleString(l10n)),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _exitEditMode,
        ),
        actions: _buildActions(l10n),
        bottom: const TabBar(
          tabs: [
            Tab(icon: Icon(Icons.trending_down), text: 'Expense Budgets'),
            Tab(icon: Icon(Icons.trending_up), text: 'Income Budgets'),
          ],
        ),
      );
    }

    // For normal mode, use horizontal TimePeriodSelector app bar with future navigation enabled
    return AppBarHelpers.createHorizontalTimePeriodAppBar(
      title: l10n.budgets,
      automaticallyImplyLeading: false, // Remove back button
      actions: _buildActions(l10n),
      allowFutureNavigation:
          true, // Enable future navigation for budget templates
      bottom: const TabBar(
        tabs: [
          Tab(icon: Icon(Icons.trending_down), text: 'Expense Budgets'),
          Tab(icon: Icon(Icons.trending_up), text: 'Income Budgets'),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final appBarWidget = _buildBudgetsAppBar(l10n);

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        // Only set appBar if in edit mode (traditional AppBar)
        appBar: _isEditMode ? appBarWidget as PreferredSizeWidget : null,
        body: Column(
          children: [
            // Show horizontal app bar when not in edit mode
            if (!_isEditMode) appBarWidget,

            // Template mode indicator for future periods
            _buildTemplateModeIndicator(context),

            // Tab content
            Expanded(
              child: TabBarView(
                children: [
                  _buildCategoryBudgetList(CategoryType.expense),
                  _buildCategoryBudgetList(CategoryType.income),
                ],
              ),
            ),
          ],
        ),
      ).withGlobalFabs(),
    );
  }

  /// Builds the actions list based on current mode
  List<Widget> _buildActions(AppLocalizations l10n) {
    if (_isEditMode) {
      return [
        IconButton(
          icon: const Icon(Icons.save),
          onPressed: _hasUnsavedChanges() ? _saveChanges : null,
          tooltip: 'Save Changes',
        ),
      ];
    } else {
      return [
        TextButton(
          onPressed: _enterEditMode,
          child: const Text('Edit Budget'),
        ),
      ];
    }
  }

  Widget _buildCategoryBudgetList(CategoryType categoryType) {
    final categoryBudgetInfoAsync = ref.watch(
      categoryBudgetInfoProvider(categoryType),
    );
    final summaryAsync = ref.watch(
      categoryTypeBudgetSummaryProvider(categoryType),
    );

    return RefreshIndicator(
      onRefresh: () async {
        ref
          ..invalidate(categoryBudgetInfoProvider(categoryType))
          ..invalidate(categoryTypeBudgetSummaryProvider(categoryType));
      },
      child: CustomScrollView(
        slivers: [
          // Total budget card for this category type
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(DesignTokens.spacing16),
              child: _buildTotalBudgetCard(categoryType),
            ),
          ),

          // Budget overview card
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: DesignTokens.spacing16,
              ),
              child: summaryAsync.when(
                loading: () =>
                    const SizedBox(height: 120, child: LoadingIndicator()),
                error: (error, stackTrace) => ErrorDisplay(
                  error: error,
                  onRetry: () => ref.invalidate(
                    categoryTypeBudgetSummaryProvider(categoryType),
                  ),
                ),
                data: (summary) => BudgetOverviewCard(summary: summary),
              ),
            ),
          ),

          // Category budget cards
          categoryBudgetInfoAsync.when(
            loading: () => const SliverToBoxAdapter(child: LoadingIndicator()),
            error: (error, stackTrace) => SliverToBoxAdapter(
              child: ErrorDisplay(
                error: error,
                onRetry: () =>
                    ref.invalidate(categoryBudgetInfoProvider(categoryType)),
              ),
            ),
            data: (categoryBudgetList) {
              if (categoryBudgetList.isEmpty) {
                return SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(DesignTokens.spacing16),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(
                            categoryType == CategoryType.expense
                                ? Icons.trending_down
                                : Icons.trending_up,
                            size: 64,
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(height: DesignTokens.spacing16),
                          Text(
                            'No ${categoryType.name} categories found',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: DesignTokens.spacing8),
                          Text(
                            'Create ${categoryType.name} categories to start budgeting.',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurfaceVariant,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }

              return SliverList.builder(
                itemCount: categoryBudgetList.length,
                itemBuilder: (context, index) {
                  final categoryBudgetInfo = categoryBudgetList[index];
                  return Padding(
                    padding: EdgeInsets.only(
                      left: DesignTokens.spacing16,
                      right: DesignTokens.spacing16,
                      bottom: index == categoryBudgetList.length - 1
                          ? 80.0 // Extra space for FAB
                          : DesignTokens.spacing12,
                    ),
                    child: CategoryBudgetCard(
                      categoryBudgetInfo: categoryBudgetInfo,
                      onTap: () => _navigateToTransactions(
                        categoryBudgetInfo.category.id,
                      ),
                      onEditBudget: () => categoryBudgetInfo.budget != null
                          ? _navigateToBudgetEdit(categoryBudgetInfo.budget!.id)
                          : null,
                      isEditMode: _isEditMode,
                      budgetController: categoryBudgetInfo.budget != null
                          ? _getBudgetController(categoryBudgetInfo.budget!.id)
                          : null,
                      onBudgetAmountChanged: categoryBudgetInfo.budget != null
                          ? (value) => _onBudgetAmountChanged(
                              categoryBudgetInfo.budget!.id,
                              value,
                            )
                          : null,
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  /// Navigate to budget edit screen
  void _navigateToBudgetEdit(String budgetId) {
    context.push('/budgets/$budgetId/edit');
  }

  /// Navigate to transactions filtered by category
  void _navigateToTransactions(String categoryId) {
    context.push('/transactions?categoryId=$categoryId');
  }

  /// Enter edit mode
  void _enterEditMode() {
    setState(() {
      _isEditMode = true;
    });
  }

  /// Exit edit mode
  void _exitEditMode() {
    if (_hasUnsavedChanges()) {
      _showUnsavedChangesDialog();
    } else {
      _clearEditMode();
    }
  }

  /// Clear edit mode state
  void _clearEditMode() {
    setState(() {
      _isEditMode = false;
      _editedBudgetAmounts.clear();
      // Dispose and clear controllers
      for (final controller in _budgetControllers.values) {
        controller.dispose();
      }
      _budgetControllers.clear();
    });
  }

  /// Check if there are unsaved changes
  bool _hasUnsavedChanges() {
    return _editedBudgetAmounts.isNotEmpty;
  }

  /// Get or create text controller for budget
  TextEditingController _getBudgetController(String budgetId) {
    if (!_budgetControllers.containsKey(budgetId)) {
      // Find the budget to get its current amount
      final currentPeriod = ref.read(currentTimePeriodProvider);
      final budgetsAsync = ref.read(budgetsByMonthProvider(currentPeriod));

      var initialAmount = 0.0;
      budgetsAsync.whenData((budgets) {
        try {
          final budget = budgets.firstWhere((b) => b.id == budgetId);
          initialAmount = budget.plannedAmountCents / 100.0;
        } on Exception {
          // Budget not found, use default amount
          initialAmount = 0.0;
        }
      });

      _budgetControllers[budgetId] = TextEditingController(
        text: initialAmount.toStringAsFixed(2),
      );
    }
    return _budgetControllers[budgetId]!;
  }

  /// Handle budget amount change
  void _onBudgetAmountChanged(String budgetId, String value) {
    final amount = double.tryParse(value);
    if (amount != null && amount >= 0) {
      setState(() {
        _editedBudgetAmounts[budgetId] = amount;
      });
    } else {
      setState(() {
        _editedBudgetAmounts.remove(budgetId);
      });
    }
  }

  /// Save all changes
  Future<void> _saveChanges() async {
    if (!_hasUnsavedChanges()) return;

    try {
      final budgetController = ref.read(budgetsControllerProvider.notifier);
      final currentPeriod = ref.read(currentTimePeriodProvider);
      final budgetsAsync = await ref.read(
        budgetsByMonthProvider(currentPeriod).future,
      );

      // Update each edited budget
      for (final entry in _editedBudgetAmounts.entries) {
        final budgetId = entry.key;
        final newAmount = entry.value;

        // Find the budget to update
        final budgetList = budgetsAsync.where((b) => b.id == budgetId);
        if (budgetList.isEmpty) {
          throw StateError('Budget not found: $budgetId');
        }
        final budget = budgetList.first;

        // Create updated budget with new amount
        final updatedBudget = budget.copyWith(
          plannedAmountCents: (newAmount * 100).round(),
        );

        // Update the budget
        await budgetController.updateBudget(updatedBudget);
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_editedBudgetAmounts.length} budget(s) updated successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }

      // Clear edit mode
      _clearEditMode();
    } on Exception catch (error) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving budgets: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Show unsaved changes dialog
  void _showUnsavedChangesDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Unsaved Changes'),
        content: const Text(
          'You have unsaved changes. Do you want to save them before exiting edit mode?',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearEditMode();
            },
            child: const Text('Discard'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _saveChanges();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  /// Builds the template mode indicator for future periods
  Widget _buildTemplateModeIndicator(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final isFuture = ref.watch(isFuturePeriodProvider);

        if (!isFuture) {
          return const SizedBox.shrink();
        }

        final theme = Theme.of(context);

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacing16,
            vertical: DesignTokens.spacing12,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.preview,
                size: 20,
                color: theme.colorScheme.onPrimaryContainer,
              ),
              const SizedBox(width: DesignTokens.spacing8),
              Expanded(
                child: Text(
                  'Template Mode: Viewing budget templates for future period',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Icon(
                Icons.info_outline,
                size: 16,
                color: theme.colorScheme.onPrimaryContainer.withValues(
                  alpha: 0.7,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Builds the total budget card for the given category type
  Widget _buildTotalBudgetCard(CategoryType categoryType) {
    return Consumer(
      builder: (context, ref, child) {
        final selectedPeriod = ref.watch(timePeriodNotifierProvider);
        final selectedMonth = DateTime(
          selectedPeriod.year,
          selectedPeriod.month ?? 1,
        );

        final budgetType = categoryType == CategoryType.expense
            ? BudgetType.expense
            : BudgetType.income;

        final totalBudgetAsync = ref.watch(
          totalBudgetByMonthAndTypeProvider(selectedMonth, budgetType),
        );

        return totalBudgetAsync.when(
          loading: () => const SizedBox(height: 80, child: LoadingIndicator()),
          error: (error, stackTrace) => ErrorDisplay(
            error: error,
            onRetry: () => ref.invalidate(
              totalBudgetByMonthAndTypeProvider(selectedMonth, budgetType),
            ),
          ),
          data: (totalBudget) {
            if (totalBudget == null) {
              return _buildCreateTotalBudgetCard(categoryType);
            }

            return _buildExistingTotalBudgetCard(totalBudget, categoryType);
          },
        );
      },
    );
  }

  /// Builds a card to create a total budget when none exists
  Widget _buildCreateTotalBudgetCard(CategoryType categoryType) {
    final theme = Theme.of(context);

    return Card(
      elevation: DesignTokens.elevation1,
      child: Padding(
        padding: const EdgeInsets.all(DesignTokens.spacing16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  categoryType == CategoryType.expense
                      ? Icons.trending_down
                      : Icons.trending_up,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: DesignTokens.spacing8),
                Text(
                  'Total ${categoryType.name.toUpperCase()} Budget',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: DesignTokens.spacing8),
            Text(
              'Set an overall ${categoryType.name} budget for this period',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: DesignTokens.spacing12),
            ElevatedButton.icon(
              onPressed: () {
                // TODO(budapp): Navigate to create total budget screen
              },
              icon: const Icon(Icons.add),
              label: Text('Create ${categoryType.name.toUpperCase()} Budget'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds a card for an existing total budget
  Widget _buildExistingTotalBudgetCard(
    Budget totalBudget,
    CategoryType categoryType,
  ) {
    final theme = Theme.of(context);

    return Consumer(
      builder: (context, ref, child) {
        final selectedPeriod = ref.watch(timePeriodNotifierProvider);
        final selectedMonth = DateTime(
          selectedPeriod.year,
          selectedPeriod.month ?? 1,
        );

        final progressAsync = ref.watch(
          budgetProgressProvider(totalBudget.id, selectedMonth),
        );

        return Card(
          elevation: DesignTokens.elevation1,
          child: Padding(
            padding: const EdgeInsets.all(DesignTokens.spacing16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      categoryType == CategoryType.expense
                          ? Icons.trending_down
                          : Icons.trending_up,
                      color: theme.colorScheme.primary,
                    ),
                    const SizedBox(width: DesignTokens.spacing8),
                    Expanded(
                      child: Text(
                        'Total ${categoryType.name.toUpperCase()} Budget',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                    Consumer(
                      builder: (context, ref, _) {
                        final currencyFormatter = ref.watch(
                          currencyFormatterProvider,
                        );
                        return Text(
                          currencyFormatter.formatAmount(
                            totalBudget.currentAmountCents,
                          ),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.onSurface,
                          ),
                        );
                      },
                    ),
                    const Text(' / '),
                    Consumer(
                      builder: (context, ref, _) {
                        final currencyFormatter = ref.watch(
                          currencyFormatterProvider,
                        );
                        return Text(
                          currencyFormatter.formatAmount(
                            totalBudget.plannedAmountCents,
                          ),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: DesignTokens.spacing12),

                // Progress
                progressAsync.when(
                  loading: () => const LinearProgressIndicator(),
                  error: (error, stackTrace) => Text(
                    'Error loading progress',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                  data: (progress) => BudgetProgressBar(progress: progress),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Removed _formatCurrency method - now using global currency formatter
}
