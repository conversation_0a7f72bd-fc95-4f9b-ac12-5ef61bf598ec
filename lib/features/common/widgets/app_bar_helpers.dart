import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/widgets/horizontal_period_selector.dart';
import 'package:budapp/features/common/widgets/time_period_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Helper utilities for creating consistent AppBar widgets across the application
///
/// This class provides standardized AppBar creation methods that ensure
/// consistent Material 3 design, proper TimePeriodSelector integration,
/// and unified styling across all screens using the traditional Scaffold + AppBar pattern.
// ignore: avoid_classes_with_only_static_members
class AppBarHelpers {
  /// Creates an AppBar with TimePeriodSelector integration for time-dependent screens
  ///
  /// This is used for screens that need time period filtering (Home, Transactions, Budgets).
  /// The TimePeriodSelector is positioned in the left side of the title area.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display on the right side
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createTimePeriodAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    // Create calendar button for period selection
    const calendarButton = _CalendarPeriodButton();

    // Combine calendar button with other actions
    final allActions = [
      calendarButton,
      if (actions != null) ...actions,
    ];

    return AppBar(
      title: _buildTimePeriodTitle(title),
      titleSpacing: 16,
      centerTitle: false, // Left align title
      actions: allActions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates an AppBar with TimePeriodSelector integration for scrollable screens
  ///
  /// This is used for screens that need time period filtering (Home, Transactions, Budgets).
  /// The TimePeriodSelector is positioned in the left side of the title area.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display on the right side
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  /// - [allowFutureNavigation]: Whether to allow navigation to future periods (for budget templates)
  static AppBar createTimePeriodScrollableAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
    bool allowFutureNavigation = false,
    bool automaticallyImplyLeading = true,
  }) {
    // Create calendar button for period selection
    final calendarButton = _CalendarPeriodButton(
      allowFutureNavigation: allowFutureNavigation,
    );

    // Combine calendar button with other actions
    final allActions = [
      calendarButton,
      if (actions != null) ...actions,
    ];

    return AppBar(
      title: _buildTimePeriodTitle(
        title,
        allowFutureNavigation: allowFutureNavigation,
      ),
      titleSpacing: 16,
      centerTitle: false, // Left align title
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: allActions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a standard AppBar for screens that don't need time period filtering
  ///
  /// This is used for screens like Profile, Settings, Account Details, etc.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [centerTitle]: Whether to center the title (default: true)
  /// - [automaticallyImplyLeading]: Whether to show automatic back button (default: true)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createStandardAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    bool centerTitle = false, // Changed default to false for left alignment
    bool automaticallyImplyLeading =
        false, // Changed default to false to remove back buttons
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: Text(title),
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a standard AppBar for scrollable screens without time period filtering
  ///
  /// This is used for screens like Categories, Tags, etc. that use SingleChildScrollView
  /// but don't need time period functionality.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [automaticallyImplyLeading]: Whether to show automatic back button (default: true)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  static AppBar createStandardScrollableAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    bool automaticallyImplyLeading =
        false, // Changed default to false to remove back buttons
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return AppBar(
      title: Text(title),
      centerTitle: false, // Left align title
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: actions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );
  }

  /// Creates a flexible AppBar for transactions with contextual information and summary
  ///
  /// This method returns a Column containing AppBar, optional context section,
  /// HorizontalPeriodSelector, and optional summary section.
  /// Designed specifically for transactions screens with filtering capabilities.
  ///
  /// Parameters:
  /// - [title]: The main screen title (typically "Transactions")
  /// - [actions]: Action buttons for the right side of the AppBar
  /// - [contextInfo]: Optional contextual info (e.g., "Account - Savings")
  /// - [summaryWidget]: Optional summary widget for filtered views
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  /// - [allowFutureNavigation]: Whether to allow navigation to future periods
  /// - [automaticallyImplyLeading]: Whether to show automatic back button (default: false)
  static Widget createFlexibleTransactionAppBar({
    required String title,
    List<Widget>? actions,
    String? contextInfo,
    Widget? summaryWidget,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
    bool allowFutureNavigation = false,
    bool automaticallyImplyLeading = false,
  }) {
    // Create calendar button for period selection
    final calendarButton = _CalendarPeriodButton(
      allowFutureNavigation: allowFutureNavigation,
    );

    // Combine calendar button with other actions
    final allActions = [
      calendarButton,
      if (actions != null) ...actions,
    ];

    final appBar = AppBar(
      title: Text(title),
      titleSpacing: 16,
      centerTitle: false, // Left align title
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: allActions,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Main AppBar
        appBar,

        // Optional context information section
        if (contextInfo != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(
              horizontal: DesignTokens.spacing16,
              vertical: DesignTokens.spacing8,
            ),
            decoration: BoxDecoration(
              color: backgroundColor,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withValues(alpha: 0.1),
                  width: 0.5,
                ),
              ),
            ),
            child: Text(
              contextInfo,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

        // Horizontal Period Selector
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacing16,
            vertical: DesignTokens.spacing8,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
          ),
          child: HorizontalPeriodSelector(
            allowFutureNavigation: allowFutureNavigation,
          ),
        ),

        // Optional summary widget
        if (summaryWidget != null) summaryWidget,
      ],
    );
  }

  /// Creates an AppBar with HorizontalPeriodSelector integration for time-dependent screens
  ///
  /// This method returns a Column containing both the AppBar and HorizontalPeriodSelector.
  /// It's designed for screens that need horizontal scrollable period selection.
  ///
  /// Parameters:
  /// - [title]: The screen title text to display
  /// - [actions]: Optional action buttons for the right side of the AppBar
  /// - [bottom]: Optional bottom widget (e.g., TabBar)
  /// - [elevation]: Custom elevation (defaults to AppElevation.sm)
  /// - [backgroundColor]: Custom background color (defaults to theme surface)
  /// - [foregroundColor]: Custom foreground color (defaults to theme onSurface)
  /// - [allowFutureNavigation]: Whether to allow navigation to future periods (for budget templates)
  /// - [automaticallyImplyLeading]: Whether to show automatic back button (default: false)
  static Widget createHorizontalTimePeriodAppBar({
    required String title,
    List<Widget>? actions,
    PreferredSizeWidget? bottom,
    double? elevation,
    Color? backgroundColor,
    Color? foregroundColor,
    bool allowFutureNavigation = false,
    bool automaticallyImplyLeading = false,
  }) {
    // Create calendar button for period selection as fallback
    final calendarButton = _CalendarPeriodButton(
      allowFutureNavigation: allowFutureNavigation,
    );

    // Combine calendar button with other actions
    final allActions = [
      calendarButton,
      if (actions != null) ...actions,
    ];

    final appBar = AppBar(
      title: Text(title),
      titleSpacing: 16,
      centerTitle: false, // Left align title
      automaticallyImplyLeading: automaticallyImplyLeading,
      actions: allActions,
      bottom: bottom,
      elevation: elevation ?? AppElevation.sm,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
    );

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Use a SizedBox to constrain the AppBar height when it has a bottom widget
        if (bottom != null)
          SizedBox(
            height: kToolbarHeight + bottom.preferredSize.height,
            child: appBar,
          )
        else
          appBar,
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(
            horizontal: DesignTokens.spacing16,
            vertical: DesignTokens.spacing8,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
          ),
          child: HorizontalPeriodSelector(
            allowFutureNavigation: allowFutureNavigation,
          ),
        ),
      ],
    );
  }

  /// Builds the title widget with TimePeriodSelector integration
  ///
  /// This creates just the title text, as the TimePeriodSelector will be
  /// moved to the actions area as a calendar button.
  static Widget _buildTimePeriodTitle(
    String title, {
    bool allowFutureNavigation = false,
  }) {
    return Text(title);
  }
}

/// Calendar button widget for period selection in app bar actions
class _CalendarPeriodButton extends ConsumerWidget {
  const _CalendarPeriodButton({
    this.allowFutureNavigation = false,
  });

  final bool allowFutureNavigation;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return IconButton(
      icon: const Icon(Icons.calendar_month),
      tooltip: 'Select Period',
      onPressed: () => _showPeriodModal(context, ref),
    );
  }

  Future<void> _showPeriodModal(BuildContext context, WidgetRef ref) async {
    final selectedPeriod = await showModalBottomSheet<TimePeriod>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const TimePeriodModal(),
    );

    if (selectedPeriod != null) {
      await ref
          .read(timePeriodNotifierProvider.notifier)
          .selectPeriod(selectedPeriod);
    }
  }
}
