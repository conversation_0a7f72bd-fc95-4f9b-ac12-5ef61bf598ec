import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/models/time_period.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/services/time_period_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A horizontal scrollable period selector widget that displays periods in a compact format.
///
/// Features:
/// - Horizontal scrolling with PageView.builder for smooth navigation
/// - Snap-to-center behavior for precise period selection
/// - Bidirectional synchronization with global time period state
/// - Material 3 design with proper accessibility support
/// - Efficient lazy loading of periods using TimePeriodService.getHorizontalPeriods()
/// - Current period highlighting with subtle visual emphasis
class HorizontalPeriodSelector extends ConsumerStatefulWidget {
  const HorizontalPeriodSelector({
    super.key,
    this.onPeriodChanged,
    this.height = 44.0,
    this.allowFutureNavigation = false,
  });

  /// Callback invoked when the period selection changes
  final VoidCallback? onPeriodChanged;

  /// Height of the selector widget (minimum 44px for accessibility)
  final double height;

  /// Whether to allow navigation to future periods
  final bool allowFutureNavigation;

  @override
  ConsumerState<HorizontalPeriodSelector> createState() =>
      _HorizontalPeriodSelectorState();
}

class _HorizontalPeriodSelectorState
    extends ConsumerState<HorizontalPeriodSelector>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentPageIndex = 0;
  bool _isInitialized = false;
  bool _isScrolling = false;
  bool _isLoading = false;
  TimePeriod? _lastSyncedPeriod;

  // Infinite scroll constants
  static const int _totalItems = 50000; // Large range for "infinite" scroll
  static const int _centerIndex = 25000; // Center of range

  // Performance optimization: cache rendered period items
  final Map<int, Widget> _cachedPeriodItems = <int, Widget>{};

  // Responsive design constants
  static const double _minViewportFraction =
      0.20; // Show 5 items on wide screens
  static const double _maxViewportFraction =
      0.33; // Show 3 items on narrow screens
  static const double _breakpoint = 400; // Screen width breakpoint

  /// Convert year/month to months since reference date (Jan 2000 = offset 0)
  int _yearMonthToOffset(int year, int month) {
    return (year - 2000) * 12 + (month - 1);
  }

  /// Convert months offset back to year/month
  (int year, int month) _offsetToYearMonth(int offset) {
    final year = 2000 + (offset ~/ 12);
    final month = (offset % 12) + 1;
    return (year, month);
  }

  /// Generate a TimePeriod from a PageView index using mathematical calculation
  TimePeriod _generatePeriodFromIndex(int index) {
    // Get current month offset for center alignment
    final currentMonth = TimePeriodService.getCurrentMonth();
    final currentMonthOffset = _yearMonthToOffset(
      currentMonth.year,
      currentMonth.month!,
    );

    // Calculate target month offset from index
    final targetMonthOffset = currentMonthOffset + (index - _centerIndex);

    // Convert offset back to year/month
    final (targetYear, targetMonthValue) = _offsetToYearMonth(
      targetMonthOffset,
    );

    // Use TimePeriodService to create the period with proper formatting
    return TimePeriodService.getMonthPeriod(targetYear, targetMonthValue);
  }

  /// Get PageView index from a TimePeriod using mathematical calculation
  int _getIndexFromPeriod(TimePeriod targetPeriod) {
    final currentMonth = TimePeriodService.getCurrentMonth();
    final currentMonthOffset = _yearMonthToOffset(
      currentMonth.year,
      currentMonth.month!,
    );
    final targetMonthOffset = _yearMonthToOffset(
      targetPeriod.year,
      targetPeriod.month!,
    );

    // Calculate index based on offset difference
    final offsetDifference = targetMonthOffset - currentMonthOffset;
    return _centerIndex + offsetDifference;
  }

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializePeriods();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );

    _fadeAnimation =
        Tween<double>(
          begin: 0,
          end: 1,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOut,
          ),
        );

    _animationController.forward();
  }

  void _initializePeriods() {
    // Find the index of the current selected period using mathematical calculation
    final selectedPeriod = ref.read(timePeriodNotifierProvider);
    final initialIndex = _getIndexFromPeriod(selectedPeriod);

    // Clamp to valid range
    final clampedIndex = initialIndex.clamp(0, _totalItems - 1);
    _currentPageIndex = clampedIndex;

    // Use responsive viewport fraction based on initial screen size
    // Note: This is initialized once; responsive updates happen in build()
    const viewportFraction = _maxViewportFraction;

    _pageController = PageController(
      initialPage: clampedIndex,
      viewportFraction: viewportFraction,
    );

    _isInitialized = true;
  }

  /// Find the index of a period using mathematical calculation
  int? _findPeriodIndex(TimePeriod targetPeriod) {
    final index = _getIndexFromPeriod(targetPeriod);
    // Validate that index is within bounds
    if (index >= 0 && index < _totalItems) {
      return index;
    }
    return null;
  }

  /// Update the page controller when external period changes occur
  void _syncWithExternalPeriodChange(TimePeriod newPeriod) {
    if (_isScrolling || !_isInitialized) return;

    final newIndex = _findPeriodIndex(newPeriod);
    if (newIndex != null && newIndex != _currentPageIndex) {
      // Move PageView immediately for synchronous state updates (like from tests)
      if (_pageController.hasClients) {
        _pageController.jumpToPage(newIndex);
      }

      setState(() {
        _currentPageIndex = newIndex;
        // Clear cache to force rebuild with new selection state
        _cachedPeriodItems.clear();
      });
    }
  }

  /// Handle page changes from user scrolling
  Future<void> _onPageChanged(int pageIndex) async {
    if (_isScrolling) return;

    setState(() {
      _currentPageIndex = pageIndex;
      _isLoading = true;
    });

    // Add haptic feedback for period selection
    await _provideFeedback();

    // Generate the selected period from the page index
    final selectedPeriod = _generatePeriodFromIndex(pageIndex);
    final notifier = ref.read(timePeriodNotifierProvider.notifier);

    try {
      if (widget.allowFutureNavigation) {
        // For budget views, allow future periods up to 12 months
        final now = DateTime.now();
        final maxFuture = DateTime(now.year + 1, now.month, 1);

        if (selectedPeriod.startDate.isBefore(maxFuture) ||
            selectedPeriod.startDate.isAtSameMomentAs(maxFuture)) {
          await notifier.selectPeriod(selectedPeriod);
          widget.onPeriodChanged?.call();
        }
      } else if (TimePeriodService.isPeriodSelectable(selectedPeriod)) {
        await notifier.selectPeriod(selectedPeriod);
        widget.onPeriodChanged?.call();
      }
    } on Exception catch (_) {
      // Handle selection error silently
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Provide haptic feedback for period selection
  Future<void> _provideFeedback() async {
    try {
      await HapticFeedback.selectionClick();
    } on Exception catch (_) {
      // Ignore haptic feedback errors on unsupported platforms
    }
  }

  /// Calculate responsive viewport fraction based on screen width
  double _calculateViewportFraction(double screenWidth) {
    if (screenWidth < _breakpoint) {
      return _maxViewportFraction;
    } else {
      // Smoothly interpolate between min and max based on screen width
      final ratio = ((screenWidth - _breakpoint) / (_breakpoint * 2)).clamp(
        0.0,
        1.0,
      );
      return _maxViewportFraction +
          ((_minViewportFraction - _maxViewportFraction) * ratio);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    _cachedPeriodItems.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return SizedBox(
        height: widget.height,
        child: Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
              ),
            ),
          ),
        ),
      );
    }

    final theme = Theme.of(context);
    final selectedPeriod = ref.watch(timePeriodNotifierProvider);
    final screenWidth = MediaQuery.of(context).size.width;
    final viewportFraction = _calculateViewportFraction(screenWidth);

    // Sync with external period changes only if period actually changed
    if (_lastSyncedPeriod != selectedPeriod) {
      _lastSyncedPeriod = selectedPeriod;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _syncWithExternalPeriodChange(selectedPeriod);
      });
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SizedBox(
        height: widget.height,
        child: Stack(
          children: [
            // Main period selector
            Semantics(
              label: 'Time period selector',
              hint:
                  'Swipe left or right to change periods, or tap a period to select it',
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _totalItems,
                itemBuilder: (context, index) => _buildPeriodItemCached(
                  context,
                  theme,
                  index,
                  viewportFraction,
                ),
              ),
            ),
            // Loading overlay
            if (_isLoading)
              Positioned.fill(
                child: ColoredBox(
                  color: theme.colorScheme.surface.withValues(alpha: 0.8),
                  child: Center(
                    child: SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build and cache period items for performance
  Widget _buildPeriodItemCached(
    BuildContext context,
    ThemeData theme,
    int index,
    double viewportFraction,
  ) {
    // Check if we have a cached version that's still valid
    final cacheKey = index;

    // Invalidate cache if the selection state changed
    final isSelected = index == _currentPageIndex;

    // For performance, only cache non-selected items as they change less frequently
    if (!isSelected && _cachedPeriodItems.containsKey(cacheKey)) {
      return _cachedPeriodItems[cacheKey]!;
    }

    final widget = _buildPeriodItem(context, theme, index, viewportFraction);

    // Cache non-selected items
    if (!isSelected) {
      _cachedPeriodItems[cacheKey] = widget;
    }

    return widget;
  }

  Widget _buildPeriodItem(
    BuildContext context,
    ThemeData theme,
    int index,
    double viewportFraction,
  ) {
    final period = _generatePeriodFromIndex(index);
    final isSelected = index == _currentPageIndex;
    final isCurrentMonth = period.isCurrent;
    final isSelectable =
        widget.allowFutureNavigation ||
        TimePeriodService.isPeriodSelectable(period);

    // Use compact formatting from TimePeriodService
    final periodText = TimePeriodService.formatPeriodCompact(period);

    // Calculate responsive padding based on viewport fraction
    final horizontalPadding = viewportFraction > 0.25
        ? DesignTokens.spacing8
        : DesignTokens.spacing4;

    return GestureDetector(
      onTap: isSelectable
          ? () {
              if (index != _currentPageIndex) {
                _performHapticFeedback();
                _isScrolling = true;
                _pageController
                    .animateToPage(
                      index,
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOutCubic,
                    )
                    .then((_) {
                      _isScrolling = false;
                      // Trigger period selection after animation completes
                      _onPageChanged(index);
                    });
              }
            }
          : null,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: DesignTokens.spacing2),
        child: Center(
          child: Semantics(
            label: '${period.displayName} period',
            hint: isSelectable
                ? 'Tap to select this period'
                : 'Period not selectable',
            button: isSelectable,
            selected: isSelected,
            excludeSemantics: !isSelectable,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 250),
              curve: Curves.easeInOutCubic,
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: DesignTokens.spacing4,
              ),
              decoration: BoxDecoration(
                color: _getPeriodBackgroundColor(
                  theme,
                  isSelected,
                  isCurrentMonth,
                  isSelectable,
                ),
                borderRadius: BorderRadius.circular(DesignTokens.borderRadius8),
                border: _getPeriodBorder(theme, isSelected, isCurrentMonth),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: theme.colorScheme.primary.withValues(
                            alpha: 0.2,
                          ),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: _getPeriodTextStyle(
                  theme,
                  isSelected,
                  isCurrentMonth,
                  isSelectable,
                ),
                child: Text(
                  periodText,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  semanticsLabel: isSelected
                      ? 'Selected period: ${period.displayName}'
                      : period.displayName,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Provide haptic feedback for touch interactions
  void _performHapticFeedback() {
    try {
      HapticFeedback.lightImpact();
    } on Exception catch (_) {
      // Ignore haptic feedback errors on unsupported platforms
    }
  }

  /// Get the background color for a period item
  Color _getPeriodBackgroundColor(
    ThemeData theme,
    bool isSelected,
    bool isCurrentMonth,
    bool isSelectable,
  ) {
    if (!isSelectable) {
      return theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3);
    } else if (isSelected) {
      return theme.colorScheme.primaryContainer;
    } else if (isCurrentMonth) {
      return theme.colorScheme.surfaceContainerHighest;
    } else {
      return Colors.transparent;
    }
  }

  /// Get the border for a period item
  Border? _getPeriodBorder(
    ThemeData theme,
    bool isSelected,
    bool isCurrentMonth,
  ) {
    if (isSelected) {
      return Border.all(
        color: theme.colorScheme.primary,
        width: 1.5,
      );
    } else if (isCurrentMonth) {
      return Border.all(
        color: theme.colorScheme.outline.withValues(alpha: 0.3),
        width: 1,
      );
    }
    return null;
  }

  /// Get the text style for a period item
  TextStyle _getPeriodTextStyle(
    ThemeData theme,
    bool isSelected,
    bool isCurrentMonth,
    bool isSelectable,
  ) {
    final baseStyle = theme.textTheme.bodyMedium?.copyWith(
      fontWeight: FontWeight.w500,
    );

    if (!isSelectable) {
      return baseStyle?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            fontWeight: FontWeight.w400,
          ) ??
          TextStyle(color: theme.colorScheme.onSurface.withValues(alpha: 0.4));
    } else if (isSelected) {
      return baseStyle?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.1,
          ) ??
          TextStyle(color: theme.colorScheme.onPrimaryContainer);
    } else if (isCurrentMonth) {
      return baseStyle?.copyWith(
            color: theme.colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ) ??
          TextStyle(color: theme.colorScheme.onSurface);
    } else {
      return baseStyle?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ) ??
          TextStyle(color: theme.colorScheme.onSurfaceVariant);
    }
  }
}
