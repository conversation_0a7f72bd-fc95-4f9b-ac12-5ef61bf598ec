import 'package:budapp/features/common/models/time_period.dart';
import 'package:intl/intl.dart';

/// Service class for managing time periods and date calculations
// ignore: avoid_classes_with_only_static_members
class TimePeriodService {
  static const String _storageKey = 'selected_time_period';

  /// Get the current month as a TimePeriod
  static TimePeriod getCurrentMonth() {
    final now = DateTime.now();
    return getMonthPeriod(now.year, now.month);
  }

  /// Get a specific month as a TimePeriod
  static TimePeriod getMonthPeriod(int year, int month) {
    final startDate = DateTime(year, month, 1);
    // Use DateTime(year, month + 1, 0) to get the last day of the month
    // This avoids DST issues that can occur with subtraction
    final endDate = DateTime(year, month + 1, 0);
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month, 1);
    final periodMonth = DateTime(year, month, 1);

    return TimePeriod(
      type: PeriodType.monthly,
      startDate: startDate,
      endDate: endDate,
      displayName: _formatMonthYear(year, month),
      dateRangeText: _formatDateRange(startDate, endDate),
      year: year,
      month: month,
      isPast: periodMonth.isBefore(currentMonth),
      isCurrent: periodMonth.isAtSameMomentAs(currentMonth),
    );
  }

  /// Get the previous month from the given period
  static TimePeriod getPreviousMonth(TimePeriod current) {
    if (current.type != PeriodType.monthly || current.month == null) {
      throw ArgumentError('Can only get previous month for monthly periods');
    }

    final currentMonth = current.month!;
    final currentYear = current.year;

    if (currentMonth == 1) {
      return getMonthPeriod(currentYear - 1, 12);
    } else {
      return getMonthPeriod(currentYear, currentMonth - 1);
    }
  }

  /// Get the next month from the given period
  static TimePeriod getNextMonth(TimePeriod current) {
    if (current.type != PeriodType.monthly || current.month == null) {
      throw ArgumentError('Can only get next month for monthly periods');
    }

    final currentMonth = current.month!;
    final currentYear = current.year;

    if (currentMonth == 12) {
      return getMonthPeriod(currentYear + 1, 1);
    } else {
      return getMonthPeriod(currentYear, currentMonth + 1);
    }
  }

  /// Get a list of available periods (past and current months only)
  /// Returns the last 24 months plus current month
  static List<TimePeriod> getAvailablePeriods() {
    final periods = <TimePeriod>[];
    final now = DateTime.now();

    // Start from 24 months ago
    var currentDate = DateTime(now.year - 2, now.month, 1);
    final endDate = DateTime(now.year, now.month, 1);

    while (currentDate.isBefore(endDate) ||
        currentDate.isAtSameMomentAs(endDate)) {
      periods.add(getMonthPeriod(currentDate.year, currentDate.month));

      // Move to next month
      if (currentDate.month == 12) {
        currentDate = DateTime(currentDate.year + 1, 1, 1);
      } else {
        currentDate = DateTime(currentDate.year, currentDate.month + 1, 1);
      }
    }

    // Sort in descending order (newest first)
    periods.sort((a, b) => b.startDate.compareTo(a.startDate));

    return periods;
  }

  /// Check if a period is valid for selection
  /// Now allows future periods up to 5 years ahead since we have fallback logic
  static bool isPeriodSelectable(TimePeriod period) {
    final now = DateTime.now();
    final periodStart = DateTime(
      period.startDate.year,
      period.startDate.month,
      1,
    );

    // Allow periods up to 5 years in the future
    final maxFutureDate = DateTime(now.year + 5, now.month, 1);

    return periodStart.isBefore(maxFutureDate) ||
        periodStart.isAtSameMomentAs(maxFutureDate);
  }

  /// Format month and year for display (e.g., "December 2024")
  static String _formatMonthYear(int year, int month) {
    final date = DateTime(year, month, 1);
    return DateFormat('MMMM yyyy').format(date);
  }

  /// Format date range for display (e.g., "01 Dec - 31 Dec")
  static String _formatDateRange(DateTime startDate, DateTime endDate) {
    final startFormat = DateFormat('dd MMM');
    final endFormat = DateFormat('dd MMM');

    return '${startFormat.format(startDate)} - ${endFormat.format(endDate)}';
  }

  /// Get the storage key for persisting selected period
  static String get storageKey => _storageKey;

  /// Convert TimePeriod to JSON string for storage
  static String periodToStorageString(TimePeriod period) {
    return '${period.year}_${period.month}';
  }

  /// Convert storage string back to TimePeriod
  static TimePeriod? periodFromStorageString(String? storageString) {
    if (storageString == null || storageString.isEmpty) {
      return null;
    }

    try {
      final parts = storageString.split('_');
      if (parts.length != 2) return null;

      final year = int.parse(parts[0]);
      final month = int.parse(parts[1]);

      // Validate the period is selectable
      final period = getMonthPeriod(year, month);
      if (!isPeriodSelectable(period)) {
        return null;
      }

      return period;
    } on Exception {
      return null;
    }
  }

  /// Get the current week as a TimePeriod (for future extensibility)
  static TimePeriod getCurrentWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return TimePeriod(
      type: PeriodType.weekly,
      startDate: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      endDate: DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day),
      displayName: 'Week of ${DateFormat('MMM dd').format(startOfWeek)}',
      dateRangeText: _formatDateRange(startOfWeek, endOfWeek),
      year: now.year,
      isCurrent: true,
    );
  }

  /// Get the current year as a TimePeriod (for future extensibility)
  static TimePeriod getCurrentYear() {
    final now = DateTime.now();
    final startDate = DateTime(now.year, 1, 1);
    final endDate = DateTime(now.year, 12, 31);

    return TimePeriod(
      type: PeriodType.yearly,
      startDate: startDate,
      endDate: endDate,
      displayName: now.year.toString(),
      dateRangeText: _formatDateRange(startDate, endDate),
      year: now.year,
      isCurrent: true,
    );
  }

  /// Get a range of periods for horizontal scroll implementation
  /// Returns 12 past periods + current + 12 future periods (25 total)
  /// Optimized for infinite scroll with consistent range around current month
  static List<TimePeriod> getHorizontalPeriods() {
    final currentPeriod = getCurrentMonth();
    return getPeriodRange(currentPeriod, 25);
  }

  /// Get a range of periods centered around a specific period
  /// [center] - The central period to build the range around
  /// [count] - Total number of periods to return (should be odd for symmetric range)
  /// Returns periods sorted chronologically (oldest first)
  static List<TimePeriod> getPeriodRange(TimePeriod center, int count) {
    if (count <= 0) {
      throw ArgumentError('Count must be greater than 0');
    }

    if (center.type != PeriodType.monthly || center.month == null) {
      throw ArgumentError('Period range is only supported for monthly periods');
    }

    final periods = <TimePeriod>[];
    final halfRange = (count - 1) ~/ 2;

    // Start from the earliest period in the range
    var currentPeriod = center;

    // Navigate to the start of the range
    for (var i = 0; i < halfRange; i++) {
      currentPeriod = getPreviousMonth(currentPeriod);
    }

    // Generate all periods in the range
    for (var i = 0; i < count; i++) {
      periods.add(currentPeriod);

      // Don't get next month for the last iteration
      if (i < count - 1) {
        currentPeriod = getNextMonth(currentPeriod);
      }
    }

    return periods;
  }

  /// Format a period in compact format for horizontal display
  /// Returns format like "Jul 25" for July 2025
  /// [period] - The period to format
  static String formatPeriodCompact(TimePeriod period) {
    if (period.type != PeriodType.monthly || period.month == null) {
      throw ArgumentError(
        'Compact formatting is only supported for monthly periods',
      );
    }

    final date = DateTime(period.year, period.month!, 1);

    // Format as "MMM yy" (e.g., "Jul 25")
    return DateFormat('MMM yy').format(date);
  }

  /// Get periods for infinite scroll implementation
  /// [centerPeriod] - The current center period
  /// [direction] - 'past' for earlier periods, 'future' for later periods
  /// [count] - Number of periods to load (default: 12)
  /// Returns periods in chronological order
  static List<TimePeriod> getAdjacentPeriods(
    TimePeriod centerPeriod,
    String direction, {
    int count = 12,
  }) {
    if (centerPeriod.type != PeriodType.monthly || centerPeriod.month == null) {
      throw ArgumentError(
        'Adjacent periods only supported for monthly periods',
      );
    }

    if (!['past', 'future'].contains(direction)) {
      throw ArgumentError('Direction must be either "past" or "future"');
    }

    if (count <= 0) {
      throw ArgumentError('Count must be greater than 0');
    }

    final periods = <TimePeriod>[];
    var currentPeriod = centerPeriod;

    for (var i = 0; i < count; i++) {
      if (direction == 'past') {
        currentPeriod = getPreviousMonth(currentPeriod);
      } else {
        currentPeriod = getNextMonth(currentPeriod);
      }

      periods.add(currentPeriod);
    }

    // For past periods, reverse to maintain chronological order
    if (direction == 'past') {
      return periods.reversed.toList();
    }

    return periods;
  }
}
