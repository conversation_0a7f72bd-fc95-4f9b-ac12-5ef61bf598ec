import 'package:budapp/routing/app_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// FAB mode enumeration for different screen types
enum FabMode {
  /// Standard navigation FABs (back, home, add transaction)
  navigation,

  /// Form create mode FABs (save, cancel)
  formCreate,

  /// Form edit mode FABs (save, cancel, delete)
  formEdit,
}

/// Global FAB system that provides consistent floating action buttons across all screens
class GlobalFabSystem extends ConsumerStatefulWidget {
  const GlobalFabSystem({
    super.key,
    required this.child,
    this.mode = FabMode.navigation,
    // Navigation FAB parameters
    this.showBackFab = true,
    this.showHomeFab = false,
    this.showAddTransactionFab = true,
    // Form FAB parameters
    this.onSave,
    this.onCancel,
    this.onDelete,
    this.isLoading = false,
  });

  final Widget child;
  final FabMode mode;

  // Navigation FAB properties
  final bool showBackFab;
  final bool showHomeFab;
  final bool showAddTransactionFab;

  // Form FAB properties
  final VoidCallback? onSave;
  final VoidCallback? onCancel;
  final VoidCallback? onDelete;
  final bool isLoading;

  @override
  ConsumerState<GlobalFabSystem> createState() => _GlobalFabSystemState();
}

class _GlobalFabSystemState extends ConsumerState<GlobalFabSystem> {
  // Generate unique ID for this FAB system instance to avoid Hero tag conflicts
  late final String _instanceId = DateTime.now().millisecondsSinceEpoch
      .toString();

  @override
  void dispose() {
    // Note: Cleanup is handled by route-based cleanup provider
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isFormMode =
        widget.mode == FabMode.formCreate || widget.mode == FabMode.formEdit;
    final isAnyFormActive = ref.watch(isFormModeActiveProvider);

    // Register/unregister this form system instance
    // Only register if we're in form mode AND the current route is actually a form route
    if (isFormMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final router = ref.read(goRouterProvider);
        final currentLocation =
            router.routerDelegate.currentConfiguration.uri.path;
        final isCurrentRouteFormRoute = _isFormRoute(currentLocation);

        // Only register if the current route is actually a form route
        if (isCurrentRouteFormRoute) {
          final currentSystems = ref.read(activeFormSystemsProvider);
          if (!currentSystems.contains(_instanceId)) {
            ref.read(activeFormSystemsProvider.notifier).state = {
              ...currentSystems,
              _instanceId,
            };
          }
        }
      });
    } else {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final currentSystems = ref.read(activeFormSystemsProvider);
        if (currentSystems.contains(_instanceId)) {
          ref.read(activeFormSystemsProvider.notifier).state = currentSystems
              .where((id) => id != _instanceId)
              .toSet();
        }
      });
    }

    // For form modes, always show form FABs
    // For navigation mode, NEVER show FABs when any form is active (to avoid conflicts)
    final shouldShowFabs =
        isFormMode || (widget.mode == FabMode.navigation && !isAnyFormActive);

    return Scaffold(
      body: widget.child,
      floatingActionButton: shouldShowFabs ? _buildFabStack(context) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildFabStack(BuildContext context) {
    switch (widget.mode) {
      case FabMode.navigation:
        return _buildNavigationFabs(context);
      case FabMode.formCreate:
        return _buildFormCreateFabs(context);
      case FabMode.formEdit:
        return _buildFormEditFabs(context);
    }
  }

  Widget _buildNavigationFabs(BuildContext context) {
    final fabs = <Widget>[];

    // Add Transaction FAB (Green, bottom right)
    if (widget.showAddTransactionFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          right: 16,
          child: _AddTransactionFab(
            heroTag: 'add_transaction_fab_$_instanceId',
          ),
        ),
      );
    }

    // Back FAB (Grey, bottom left)
    if (widget.showBackFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 16,
          child: _BackFab(heroTag: 'back_fab_$_instanceId'),
        ),
      );
    }

    // Home FAB (Grey, center bottom)
    if (widget.showHomeFab) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Center(
            child: _HomeFab(heroTag: 'home_fab_$_instanceId'),
          ),
        ),
      );
    }

    return Stack(
      children: fabs,
    );
  }

  Widget _buildFormCreateFabs(BuildContext context) {
    final fabs = <Widget>[];

    // Save FAB (Green, bottom right)
    if (widget.onSave != null) {
      fabs.add(
        Positioned(
          bottom: 16,
          right: 16,
          child: _SaveFab(
            onPressed: widget.onSave!,
            isLoading: widget.isLoading,
            heroTag: 'save_fab_$_instanceId',
          ),
        ),
      );
    }

    // Cancel FAB (Red, bottom left)
    if (widget.onCancel != null) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 16,
          child: _CancelFab(
            onPressed: widget.onCancel!,
            isLoading: widget.isLoading,
            heroTag: 'cancel_fab_$_instanceId',
          ),
        ),
      );
    }

    return Stack(
      children: fabs,
    );
  }

  Widget _buildFormEditFabs(BuildContext context) {
    final fabs = <Widget>[];

    // Save FAB (Green, bottom right)
    if (widget.onSave != null) {
      fabs.add(
        Positioned(
          bottom: 16,
          right: 16,
          child: _SaveFab(
            onPressed: widget.onSave!,
            isLoading: widget.isLoading,
            heroTag: 'save_fab_$_instanceId',
          ),
        ),
      );
    }

    // Cancel FAB (Red, bottom left)
    if (widget.onCancel != null) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 16,
          child: _CancelFab(
            onPressed: widget.onCancel!,
            isLoading: widget.isLoading,
            heroTag: 'cancel_fab_$_instanceId',
          ),
        ),
      );
    }

    // Delete FAB (Red, center bottom)
    if (widget.onDelete != null) {
      fabs.add(
        Positioned(
          bottom: 16,
          left: 0,
          right: 0,
          child: Center(
            child: _DeleteFab(
              onPressed: widget.onDelete!,
              isLoading: widget.isLoading,
              heroTag: 'delete_fab_$_instanceId',
            ),
          ),
        ),
      );
    }

    return Stack(
      children: fabs,
    );
  }
}

/// Green FAB for adding transactions (appears on all screens)
class _AddTransactionFab extends ConsumerWidget {
  const _AddTransactionFab({required this.heroTag});

  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: () => context.push('/transactions/create'),
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: 6,
      child: const Icon(Icons.add, size: 28),
    );
  }
}

/// Grey FAB for going back (appears on all screens except home)
class _BackFab extends ConsumerWidget {
  const _BackFab({required this.heroTag});

  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: () {
        if (context.canPop()) {
          context.pop();
        } else {
          context.go('/home');
        }
      },
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 6,
      child: const Icon(Icons.arrow_back, size: 24),
    );
  }
}

/// Grey FAB for going to home (appears on screens deeper than one step from home)
class _HomeFab extends ConsumerWidget {
  const _HomeFab({required this.heroTag});

  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: () => context.go('/home'),
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 6,
      child: const Icon(Icons.home, size: 24),
    );
  }
}

/// Green FAB for saving/submitting forms
class _SaveFab extends ConsumerWidget {
  const _SaveFab({
    required this.onPressed,
    required this.heroTag,
    this.isLoading = false,
  });

  final VoidCallback onPressed;
  final bool isLoading;
  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: isLoading ? null : onPressed,
      backgroundColor: theme.colorScheme.primary,
      foregroundColor: theme.colorScheme.onPrimary,
      elevation: 6,
      child: isLoading
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.onPrimary,
                ),
              ),
            )
          : const Icon(Icons.check, size: 28),
    );
  }
}

/// Red FAB for canceling/closing forms
class _CancelFab extends ConsumerWidget {
  const _CancelFab({
    required this.onPressed,
    required this.heroTag,
    this.isLoading = false,
  });

  final VoidCallback onPressed;
  final bool isLoading;
  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: isLoading ? null : onPressed,
      backgroundColor: theme.colorScheme.error,
      foregroundColor: theme.colorScheme.onError,
      elevation: 6,
      child: const Icon(Icons.close, size: 28),
    );
  }
}

/// Red FAB for deleting entities in forms
class _DeleteFab extends ConsumerWidget {
  const _DeleteFab({
    required this.onPressed,
    required this.heroTag,
    this.isLoading = false,
  });

  final VoidCallback onPressed;
  final bool isLoading;
  final String heroTag;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    return FloatingActionButton(
      heroTag: heroTag, // Unique hero tag
      onPressed: isLoading ? null : onPressed,
      backgroundColor: theme.colorScheme.error,
      foregroundColor: theme.colorScheme.onError,
      elevation: 6,
      child: const Icon(Icons.delete_outline, size: 28),
    );
  }
}

/// Extension to easily wrap screens with FAB system
extension GlobalFabSystemExtension on Widget {
  /// Wrap widget with navigation FABs (back, home, add transaction)
  Widget withGlobalFabs({
    bool showBackFab = true,
    bool showHomeFab = false,
    bool showAddTransactionFab = true,
  }) {
    return GlobalFabSystem(
      mode: FabMode.navigation,
      showBackFab: showBackFab,
      showHomeFab: showHomeFab,
      showAddTransactionFab: showAddTransactionFab,
      child: this,
    );
  }

  /// Wrap widget with form FABs (save, cancel, delete)
  Widget withFormFabs({
    required FabMode mode,
    VoidCallback? onSave,
    VoidCallback? onCancel,
    VoidCallback? onDelete,
    bool isLoading = false,
  }) {
    assert(
      mode == FabMode.formCreate || mode == FabMode.formEdit,
      'withFormFabs can only be used with formCreate or formEdit modes',
    );

    return GlobalFabSystem(
      mode: mode,
      onSave: onSave,
      onCancel: onCancel,
      onDelete: onDelete,
      isLoading: isLoading,
      child: this,
    );
  }
}

/// Provider to determine FAB visibility based on current route
final fabVisibilityProvider = Provider<FabVisibility>((ref) {
  // This will be updated by route changes
  return const FabVisibility();
});

/// Data class for FAB visibility configuration
class FabVisibility {
  const FabVisibility({
    this.showBackFab = true,
    this.showHomeFab = false,
    this.showAddTransactionFab = true,
  });

  final bool showBackFab;
  final bool showHomeFab;
  final bool showAddTransactionFab;

  FabVisibility copyWith({
    bool? showBackFab,
    bool? showHomeFab,
    bool? showAddTransactionFab,
  }) {
    return FabVisibility(
      showBackFab: showBackFab ?? this.showBackFab,
      showHomeFab: showHomeFab ?? this.showHomeFab,
      showAddTransactionFab:
          showAddTransactionFab ?? this.showAddTransactionFab,
    );
  }
}

/// Notifier to manage FAB visibility state
class FabVisibilityNotifier extends Notifier<FabVisibility> {
  @override
  FabVisibility build() {
    return const FabVisibility();
  }

  void updateVisibility({
    bool? showBackFab,
    bool? showHomeFab,
    bool? showAddTransactionFab,
  }) {
    state = state.copyWith(
      showBackFab: showBackFab,
      showHomeFab: showHomeFab,
      showAddTransactionFab: showAddTransactionFab,
    );
  }

  void setForRoute(String route) {
    if (route == '/home') {
      // Home screen: only show add transaction FAB
      state = const FabVisibility(
        showBackFab: false,
        showHomeFab: false,
        showAddTransactionFab: true,
      );
    } else if (_isDeepRoute(route)) {
      // Deep routes: show all FABs
      state = const FabVisibility(
        showBackFab: true,
        showHomeFab: true,
        showAddTransactionFab: true,
      );
    } else {
      // Regular routes: show back and add transaction FABs
      state = const FabVisibility(
        showBackFab: true,
        showHomeFab: false,
        showAddTransactionFab: true,
      );
    }
  }

  bool _isDeepRoute(String route) {
    // Routes that are more than one step from home
    final deepRoutes = [
      '/accounts/create',
      '/accounts/:id/edit',
      '/categories/create',
      '/categories/:id/edit',
      '/transactions/create',
      '/transactions/:id/edit',
      '/tags/create',
      '/tags/:id/edit',
      '/budgets/:id/edit',
      '/goals/create',
      '/goals/:id/edit',
      '/profile/manage',
      '/profile/forgot-password',
    ];

    return deepRoutes.any((pattern) => _matchesPattern(route, pattern));
  }

  bool _matchesPattern(String route, String pattern) {
    // Simple pattern matching for routes with parameters
    final patternParts = pattern.split('/');
    final routeParts = route.split('/');

    if (patternParts.length != routeParts.length) return false;

    for (var i = 0; i < patternParts.length; i++) {
      if (patternParts[i].startsWith(':')) continue; // Parameter
      if (patternParts[i] != routeParts[i]) return false;
    }

    return true;
  }
}

final fabVisibilityNotifierProvider =
    NotifierProvider<FabVisibilityNotifier, FabVisibility>(
      FabVisibilityNotifier.new,
    );

/// Provider to track active form FAB system instances
final activeFormSystemsProvider = StateProvider<Set<String>>((ref) => {});

/// Provider to check if any form FAB system is currently active
final isFormModeActiveProvider = Provider<bool>((ref) {
  // Watch the cleanup provider to ensure it's initialized
  ref.watch(routeBasedFormCleanupProvider);

  final activeSystems = ref.watch(activeFormSystemsProvider);
  return activeSystems.isNotEmpty;
});

/// Provider that listens to route changes and cleans up stale form systems
final routeBasedFormCleanupProvider = Provider<void>((ref) {
  final router = ref.watch(goRouterProvider);

  // Listen to route changes
  router.routerDelegate.addListener(() {
    final currentLocation = router.routerDelegate.currentConfiguration.uri.path;

    // Check if current route is a form route
    final isFormRoute = _isFormRoute(currentLocation);

    // If not on a form route, clear all active form systems
    if (!isFormRoute) {
      // Use Future to avoid modifying provider during build
      Future(() {
        final activeSystems = ref.read(activeFormSystemsProvider);
        if (activeSystems.isNotEmpty) {
          ref.read(activeFormSystemsProvider.notifier).state = {};
        }
      });
    }
  });
});

/// Helper function to determine if a route is a form route
bool _isFormRoute(String path) {
  // Form routes end with /create or /edit
  return path.endsWith('/create') ||
      path.contains('/edit') ||
      path.contains('/create/') ||
      path.contains('/edit/');
}
