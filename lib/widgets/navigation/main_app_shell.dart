import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Main application shell that provides consistent navigation structure
/// for all authenticated screens
class MainAppShell extends ConsumerWidget {
  const MainAppShell({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: child,
      // No bottom navigation - using FAB-based navigation system
    );
  }
}

/// Shell route builder that wraps content in MainAppShell
Widget shellRouteBuilder(
  BuildContext context,
  GoRouterState state,
  Widget child,
) {
  return MainAppShell(child: child);
}
