import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/config/overflow_theme_extension.dart';
import 'package:budapp/config/overflow_tokens.dart';
import 'package:flutter/material.dart';

/// Smart grid view that automatically handles overflow and responsive behavior
///
/// This widget replaces problematic GridView.count implementations with
/// adaptive sizing, overflow protection, and responsive column management.
class SmartGridView extends StatelessWidget {
  const SmartGridView({
    super.key,
    required this.children,
    this.gridConstraints,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.context,
  });

  /// List of widgets to display in the grid
  final List<Widget> children;

  /// Grid constraints (uses feature grid constraints by default)
  final GridConstraints? gridConstraints;

  /// Padding around the grid
  final EdgeInsetsGeometry? padding;

  /// Whether the grid should shrink wrap its content
  final bool shrinkWrap;

  /// Scroll physics for the grid
  final ScrollPhysics? physics;

  /// Whether to add automatic keep alives
  final bool addAutomaticKeepAlives;

  /// Whether to add repaint boundaries
  final bool addRepaintBoundaries;

  /// Context for constraint selection
  final String? context;

  @override
  Widget build(BuildContext context) {
    final overflowTheme = OverflowThemeExtension.defaultTheme(context);
    final deviceSize = AppOverflowTokens.getDeviceSize(context);

    final constraints =
        gridConstraints ??
        (this.context != null
            ? AppOverflowTokens.getGridConstraints(this.context!)
            : overflowTheme.gridConstraints);

    final columnCount = constraints.getColumnsForDevice(deviceSize);
    final aspectRatio = constraints.getAspectRatioForDevice(deviceSize);

    return LayoutBuilder(
      builder: (context, layoutConstraints) {
        return GridView.builder(
          padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
          shrinkWrap: shrinkWrap,
          physics: physics,
          addAutomaticKeepAlives: addAutomaticKeepAlives,
          addRepaintBoundaries: addRepaintBoundaries,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columnCount,
            crossAxisSpacing: constraints.spacing ?? AppSpacing.md,
            mainAxisSpacing: constraints.runSpacing ?? AppSpacing.md,
            childAspectRatio: aspectRatio,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// Staggered grid view for variable height items
class SmartStaggeredGridView extends StatelessWidget {
  const SmartStaggeredGridView({
    super.key,
    required this.children,
    this.gridConstraints,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.context,
  });

  final List<Widget> children;
  final GridConstraints? gridConstraints;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final String? context;

  @override
  Widget build(BuildContext context) {
    final overflowTheme = OverflowThemeExtension.defaultTheme(context);
    final deviceSize = AppOverflowTokens.getDeviceSize(context);

    final constraints =
        gridConstraints ??
        (this.context != null
            ? AppOverflowTokens.getGridConstraints(this.context!)
            : overflowTheme.gridConstraints);

    final columnCount = constraints.getColumnsForDevice(deviceSize);

    return LayoutBuilder(
      builder: (context, layoutConstraints) {
        return _buildStaggeredLayout(
          context,
          layoutConstraints,
          columnCount,
          constraints,
        );
      },
    );
  }

  Widget _buildStaggeredLayout(
    BuildContext context,
    BoxConstraints layoutConstraints,
    int columnCount,
    GridConstraints constraints,
  ) {
    // Create columns for staggered layout
    final columns = List.generate(columnCount, (index) => <Widget>[]);
    final columnHeights = List.filled(columnCount, 0);

    // Distribute children across columns
    for (var i = 0; i < children.length; i++) {
      // Find the shortest column
      var shortestColumnIndex = 0;
      for (var j = 1; j < columnCount; j++) {
        if (columnHeights[j] < columnHeights[shortestColumnIndex]) {
          shortestColumnIndex = j;
        }
      }

      // Add child to shortest column
      columns[shortestColumnIndex].add(children[i]);

      // Estimate height (this is a simplified approach)
      columnHeights[shortestColumnIndex] += 200; // Estimated item height
    }

    return SingleChildScrollView(
      physics: physics,
      padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: columns.asMap().entries.map((entry) {
          final columnIndex = entry.key;
          final columnChildren = entry.value;

          return Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                right: columnIndex < columnCount - 1
                    ? constraints.spacing ?? AppSpacing.md
                    : 0,
              ),
              child: Column(
                children: columnChildren.map((child) {
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom: constraints.runSpacing ?? AppSpacing.md,
                    ),
                    child: child,
                  );
                }).toList(),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Adaptive grid view that switches between different layouts based on screen size
class AdaptiveGridView extends StatelessWidget {
  const AdaptiveGridView({
    super.key,
    required this.children,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.staggered = false,
    this.mobileConstraints,
    this.tabletConstraints,
    this.desktopConstraints,
  });

  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool staggered;
  final GridConstraints? mobileConstraints;
  final GridConstraints? tabletConstraints;
  final GridConstraints? desktopConstraints;

  @override
  Widget build(BuildContext context) {
    final deviceSize = AppOverflowTokens.getDeviceSize(context);

    GridConstraints constraints;
    switch (deviceSize) {
      case DeviceSize.mobile:
        constraints =
            mobileConstraints ?? AppOverflowTokens.listGridConstraints;
      case DeviceSize.tablet:
        constraints =
            tabletConstraints ?? AppOverflowTokens.featureGridConstraints;
      case DeviceSize.desktop:
        constraints =
            desktopConstraints ?? AppOverflowTokens.featureGridConstraints;
    }

    if (staggered) {
      return SmartStaggeredGridView(
        gridConstraints: constraints,
        padding: padding,
        shrinkWrap: shrinkWrap,
        physics: physics,
        children: children,
      );
    }

    return SmartGridView(
      gridConstraints: constraints,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// Responsive list view that adapts to screen size
class ResponsiveListView extends StatelessWidget {
  const ResponsiveListView({
    super.key,
    required this.children,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.enableGrid = true,
  });

  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool enableGrid;

  @override
  Widget build(BuildContext context) {
    final deviceSize = AppOverflowTokens.getDeviceSize(context);

    // Use list view on mobile, grid on larger screens
    if (!enableGrid || deviceSize == DeviceSize.mobile) {
      return ListView(
        padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
        shrinkWrap: shrinkWrap,
        physics: physics,
        children: children.map((child) {
          return Padding(
            padding: const EdgeInsets.only(bottom: AppSpacing.md),
            child: child,
          );
        }).toList(),
      );
    }

    return AdaptiveGridView(
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// Grid view builder with overflow protection
class SmartGridViewBuilder extends StatelessWidget {
  const SmartGridViewBuilder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.gridConstraints,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.context,
  });

  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final GridConstraints? gridConstraints;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final String? context;

  @override
  Widget build(BuildContext context) {
    final overflowTheme = OverflowThemeExtension.defaultTheme(context);
    final deviceSize = AppOverflowTokens.getDeviceSize(context);

    final constraints =
        gridConstraints ??
        (this.context != null
            ? AppOverflowTokens.getGridConstraints(this.context!)
            : overflowTheme.gridConstraints);

    final columnCount = constraints.getColumnsForDevice(deviceSize);
    final aspectRatio = constraints.getAspectRatioForDevice(deviceSize);

    return LayoutBuilder(
      builder: (context, layoutConstraints) {
        return GridView.builder(
          padding: padding ?? const EdgeInsets.all(AppSpacing.lg),
          shrinkWrap: shrinkWrap,
          physics: physics,
          addAutomaticKeepAlives: addAutomaticKeepAlives,
          addRepaintBoundaries: addRepaintBoundaries,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columnCount,
            crossAxisSpacing: constraints.spacing ?? AppSpacing.md,
            mainAxisSpacing: constraints.runSpacing ?? AppSpacing.md,
            childAspectRatio: aspectRatio,
          ),
          itemCount: itemCount,
          itemBuilder: itemBuilder,
        );
      },
    );
  }
}

/// Extension methods for easy grid view creation
extension SmartGridViewExtensions on List<Widget> {
  /// Creates a smart grid view from a list of widgets
  Widget toSmartGrid({
    GridConstraints? constraints,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    String? context,
  }) {
    return SmartGridView(
      gridConstraints: constraints,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      context: context,
      children: this,
    );
  }

  /// Creates an adaptive grid view from a list of widgets
  Widget toAdaptiveGrid({
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    bool staggered = false,
  }) {
    return AdaptiveGridView(
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      staggered: staggered,
      children: this,
    );
  }

  /// Creates a responsive list view from a list of widgets
  Widget toResponsiveList({
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    bool enableGrid = true,
  }) {
    return ResponsiveListView(
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      enableGrid: enableGrid,
      children: this,
    );
  }
}
