import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/widgets/forms/config/form_field_config.dart';
import 'package:budapp/widgets/forms/factory/form_field_factory.dart';
import 'package:budapp/widgets/forms/interfaces/form_field_interface.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Generic form screen that can handle both create and edit modes
///
/// This screen provides a reusable foundation for entity create/edit screens
/// by accepting a list of form field configurations and handling the common
/// form patterns like validation, submission, and loading states.
class BaseEditableFormScreen<T> extends ConsumerStatefulWidget {
  const BaseEditableFormScreen({
    super.key,
    required this.title,
    required this.fieldConfigs,
    required this.onSubmit,
    this.initialEntity,
    this.isLoading = false,
    this.submitButtonText,
    this.showDeleteButton = false,
    this.onDelete,
    this.deleteButtonText,
    this.customValidator,
    this.onFieldChanged,
    this.customActions,
  });

  /// Title displayed in the app bar
  final String title;

  /// List of form field configurations
  final List<FormFieldConfig<dynamic>> fieldConfigs;

  /// Callback when form is submitted
  final Future<void> Function(Map<String, dynamic> formData) onSubmit;

  /// Initial entity for edit mode (null for create mode)
  final T? initialEntity;

  /// Whether the form is currently loading
  final bool isLoading;

  /// Custom text for submit button
  final String? submitButtonText;

  /// Whether to show delete button (for edit mode)
  final bool showDeleteButton;

  /// Callback when delete is pressed
  final Future<void> Function()? onDelete;

  /// Custom text for delete button
  final String? deleteButtonText;

  /// Custom form validator that validates the entire form data
  final String? Function(Map<String, dynamic> data)? customValidator;

  /// Callback when any field value changes
  final void Function(String fieldKey, dynamic value)? onFieldChanged;

  /// Custom actions to show in app bar
  final List<Widget>? customActions;

  @override
  ConsumerState<BaseEditableFormScreen<T>> createState() =>
      _BaseEditableFormScreenState<T>();
}

class _BaseEditableFormScreenState<T>
    extends ConsumerState<BaseEditableFormScreen<T>> {
  final _formKey = GlobalKey<FormState>();
  final Map<String, IFormField<dynamic>> _formFields = {};
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _initializeFormFields();
  }

  @override
  void dispose() {
    // Dispose all form fields
    for (final field in _formFields.values) {
      field.dispose();
    }
    super.dispose();
  }

  void _initializeFormFields() {
    for (final config in widget.fieldConfigs) {
      // Wrap the original onChanged callback to trigger setState
      final wrappedConfig = config.copyWith(
        onChanged: (value) {
          // Call the original callback if it exists
          config.onChanged?.call(value);
          // Call the global field changed callback if it exists
          widget.onFieldChanged?.call(config.key, value);
          // Trigger a rebuild to update the UI
          if (mounted) {
            setState(() {
              // The state change is in the form field itself,
              // we just need to trigger a rebuild
            });
          }
        },
      );

      final field = FormFieldFactory.createField(wrappedConfig);
      _formFields[config.key] = field;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // AppBar
        AppBarHelpers.createStandardScrollableAppBar(
          title: widget.title,
          automaticallyImplyLeading: false, // Use form FABs instead
          actions: [
            ...?widget.customActions,
            // Note: Save, delete, and close actions are now handled by form FABs
          ],
        ),

        // Progress indicator when submitting
        if (_isSubmitting || widget.isLoading)
          LinearProgressIndicator(
            backgroundColor: theme.colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(
              theme.colorScheme.primary,
            ),
          ),

        // Form content
        Expanded(
          child: SafeArea(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppSpacing.lg),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Build form fields
                    ..._buildFormFields(),

                    // Note: Submit button is now handled by form FAB
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    ).withFormFabs(
      mode: widget.initialEntity == null
          ? FabMode.formCreate
          : FabMode.formEdit,
      onSave: _isSubmitting ? null : _handleSubmit,
      onCancel: () => context.pop(),
      onDelete: (widget.showDeleteButton && widget.onDelete != null)
          ? (_isSubmitting ? null : _handleDelete)
          : null,
      isLoading: _isSubmitting || widget.isLoading,
    );
  }

  List<Widget> _buildFormFields() {
    final widgets = <Widget>[];

    for (var i = 0; i < widget.fieldConfigs.length; i++) {
      final config = widget.fieldConfigs[i];
      final field = _formFields[config.key]!;

      widgets.add(FormFieldFactory.createWidget(config, existingField: field));

      // Add spacing between fields
      if (i < widget.fieldConfigs.length - 1) {
        widgets.add(const SizedBox(height: AppSpacing.lg));
      }
    }

    return widgets;
  }

  Future<void> _handleSubmit() async {
    if (!_validateForm()) return;

    setState(() => _isSubmitting = true);

    try {
      final formData = _collectFormData();
      await widget.onSubmit(formData);

      if (mounted) {
        // Show success message and close
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.initialEntity == null
                  ? 'Created successfully'
                  : 'Updated successfully',
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        context.pop();
      }
    } on Exception catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  Future<void> _handleDelete() async {
    if (widget.onDelete == null) return;

    final confirmed = await _showDeleteConfirmation();
    if (!confirmed) return;

    setState(() => _isSubmitting = true);

    try {
      await widget.onDelete!();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
        context.pop();
      }
    } on Exception catch (error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $error'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  bool _validateForm() {
    var isValid = true;

    // Validate all form fields
    for (final field in _formFields.values) {
      final error = field.validate();
      if (error != null) {
        isValid = false;
      }
    }

    // Trigger form validation for visual feedback
    _formKey.currentState?.validate();

    // Run custom form validator if provided
    if (widget.customValidator != null && isValid) {
      final formData = _collectFormData();
      final customError = widget.customValidator!(formData);
      if (customError != null) {
        isValid = false;
        // Show the custom validation error in a snackbar
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(customError),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }

    return isValid;
  }

  Map<String, dynamic> _collectFormData() {
    final formData = <String, dynamic>{};

    for (final entry in _formFields.entries) {
      formData[entry.key] = entry.value.value;
    }

    return formData;
  }

  Future<bool> _showDeleteConfirmation() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: const Text('Are you sure you want to delete this item?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
